{"name": "time-course-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/node": "^20.8.6", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "autoprefixer": "^10.4.16", "axios": "^1.5.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "firebase": "^10.5.2", "hls.js": "^1.4.12", "lucide-react": "^0.292.0", "next": "^14.2.33", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-pdf": "^10.1.0", "react-query": "^3.39.3", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "devDependencies": {"eslint": "^8.52.0", "eslint-config-next": "14.0.3"}}