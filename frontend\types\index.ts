// User Types
export interface User {
  _id?: string;
  firebaseUid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: 'student' | 'tutor' | 'admin';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  approvedBy?: string;
  approvalDate?: Date;
  subscriptions?: string[];
}

// Course Types
export interface Course {
  _id?: string;
  title: string;
  description: string;
  category: string;
  tutorId: string;
  duration: number;
  price: number;
  currency: string;
  modules: Module[];
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
  totalEnrollments: number;
  averageRating: number;
}

export interface Module {
  _id?: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  estimatedDuration: number;
}

export interface Lesson {
  _id?: string;
  title: string;
  description: string;
  order: number;
  videoUrl?: string;
  videoId?: string;
  materials: Material[];
  assignments: Assignment[];
  duration: number;
}

export interface Material {
  _id?: string;
  title: string;
  type: 'pdf' | 'ppt' | 'document';
  url: string;
  fileSize: number;
}

export interface Assignment {
  _id?: string;
  title: string;
  triggerTimestamp: number;
  timeLimit: number;
  questions: Question[];
  passingScore: number;
}

export interface Question {
  _id?: string;
  text: string;
  type: 'short_answer' | 'essay';
  correctAnswer?: string;
  points: number;
}

// Subscription Types
export interface Subscription {
  _id?: string;
  studentId: string;
  courseId: string;
  status: 'active' | 'expired' | 'terminated';
  startDate: Date;
  endDate: Date;
  failureCount: number;
  progress: Progress;
  paymentId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Progress {
  completedModules: string[];
  completedLessons: string[];
  assignmentResults: AssignmentResult[];
  overallProgress: number;
  isPaceBehind: boolean;
  expectedProgress: number;
}

export interface AssignmentResult {
  assignmentId: string;
  lessonId: string;
  score: number;
  totalPoints: number;
  percentage: number;
  passed: boolean;
  submittedAt: Date;
  answers: StudentAnswer[];
}

export interface StudentAnswer {
  questionId: string;
  answer: string;
  isCorrect: boolean;
  points: number;
}

// Payment Types
export interface Payment {
  _id?: string;
  studentId: string;
  courseId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentMethod: string;
  xenditPaymentId: string;
  createdAt: Date;
  updatedAt: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Analytics Types
export interface DashboardAnalytics {
  totalUsers: number;
  totalCourses: number;
  totalRevenue: number;
  totalEnrollments: number;
  newUsers: number;
  newEnrollments: number;
  completionRate: number;
  failureRate: number;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface CourseForm {
  title: string;
  description: string;
  category: string;
  duration: number;
  price: number;
  currency: string;
}

export interface LessonForm {
  title: string;
  description: string;
  order: number;
  duration: number;
}

export interface AssignmentForm {
  title: string;
  triggerTimestamp: number;
  timeLimit: number;
  questions: QuestionForm[];
  passingScore: number;
}

export interface QuestionForm {
  text: string;
  type: 'short_answer' | 'essay';
  correctAnswer?: string;
  points: number;
}