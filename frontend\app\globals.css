@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-gray-900 bg-gray-50;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }
  
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }
  
  .progress-fill {
    @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s infinite;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Video player styles */
.video-js {
  width: 100%;
  height: 100%;
}

.video-js .vjs-big-play-button {
  @apply bg-primary-600 border-primary-600;
}

.video-js .vjs-control-bar {
  @apply bg-gray-900 bg-opacity-75;
}

/* Assignment modal styles */
.assignment-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75;
}

.assignment-content {
  @apply bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto;
}

/* Progress indicators */
.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.35s;
  transform-origin: 50% 50%;
}

/* Responsive design helpers */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.skeleton-text {
  @apply h-4 bg-gray-200 rounded animate-pulse;
}

.skeleton-avatar {
  @apply w-10 h-10 bg-gray-200 rounded-full animate-pulse;
}

/* Status indicators */
.status-active {
  @apply bg-success-100 text-success-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-inactive {
  @apply bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-terminated {
  @apply bg-error-100 text-error-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-warning-100 text-warning-800 px-2 py-1 rounded-full text-xs font-medium;
}