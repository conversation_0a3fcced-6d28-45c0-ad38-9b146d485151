'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { subscriptionsAPI, coursesAPI } from '@/lib/api';
import { Subscription, Course } from '@/types';
import { BookOpen, CircleCheck as CheckCircle, Circle as XCircle, Clock, TrendingUp, TriangleAlert as AlertTriangle, Award, Calendar } from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

export default function StudentProgressPage() {
  const { id } = useParams();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchProgressData();
    }
  }, [id]);

  const fetchProgressData = async () => {
    try {
      const [subscriptionResponse, courseResponse] = await Promise.all([
        subscriptionsAPI.getSubscriptionProgress(id as string),
        subscriptionsAPI.getSubscription(id as string)
      ]);

      const subscriptionData = subscriptionResponse.data.data;
      setSubscription(subscriptionData);

      if (subscriptionData.courseId) {
        const courseData = await coursesAPI.getCourse(subscriptionData.courseId);
        setCourse(courseData.data.data);
      }
    } catch (error) {
      console.error('Error fetching progress data:', error);
      toast.error('Failed to load progress data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-success-600';
      case 'expired': return 'text-warning-600';
      case 'terminated': return 'text-error-600';
      default: return 'text-gray-600';
    }
  };

  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  if (!subscription || !course) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Progress Not Found</h2>
            <p className="text-gray-600 mb-6">
              The progress data you're looking for doesn't exist or you don't have access to it.
            </p>
            <Link href="/student/dashboard">
              <Button>Back to Dashboard</Button>
            </Link>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['student']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Course Progress
            </h1>
            <p className="text-lg text-gray-600">
              {course.title}
            </p>
          </div>

          {/* Progress Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <div className="flex items-center">
                <div className="bg-primary-100 p-3 rounded-full">
                  <TrendingUp className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Overall Progress</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {subscription.progress.overallProgress}%
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-success-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed Modules</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {subscription.progress.completedModules.length}
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-warning-100 p-3 rounded-full">
                  <XCircle className="h-6 w-6 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Failures</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {subscription.failureCount}/3
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-error-100 p-3 rounded-full">
                  <Calendar className="h-6 w-6 text-error-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Days Remaining</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {getDaysRemaining(subscription.endDate.toString())}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Status and Warnings */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Subscription Status */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Subscription Status
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className={`font-medium capitalize ${getStatusColor(subscription.status)}`}>
                    {subscription.status}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Start Date:</span>
                  <span className="font-medium">
                    {new Date(subscription.startDate).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">End Date:</span>
                  <span className="font-medium">
                    {new Date(subscription.endDate).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Expected Progress:</span>
                  <span className="font-medium">
                    {subscription.progress.expectedProgress}%
                  </span>
                </div>
              </div>
            </Card>

            {/* Pace Warning */}
            {subscription.progress.isPaceBehind && (
              <Card className="border-l-4 border-warning-500 bg-warning-50">
                <div className="flex items-start">
                  <AlertTriangle className="h-6 w-6 text-warning-600 mt-1" />
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-warning-800 mb-2">
                      Behind Schedule
                    </h3>
                    <p className="text-warning-700 mb-4">
                      You're behind the recommended pace. You should have completed{' '}
                      {subscription.progress.expectedProgress}% by now, but you've only completed{' '}
                      {subscription.progress.overallProgress}%.
                    </p>
                    <div className="space-y-2 text-sm text-warning-700">
                      <p>• Consider dedicating more time to studying</p>
                      <p>• Review previous lessons if needed</p>
                      <p>• Contact support if you need help</p>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {/* Failure Warning */}
            {subscription.failureCount > 0 && (
              <Card className="border-l-4 border-error-500 bg-error-50">
                <div className="flex items-start">
                  <XCircle className="h-6 w-6 text-error-600 mt-1" />
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-error-800 mb-2">
                      Assignment Failures
                    </h3>
                    <p className="text-error-700 mb-4">
                      You have {subscription.failureCount} assignment failure{subscription.failureCount > 1 ? 's' : ''}.
                      {subscription.failureCount >= 2 && ' Be careful - one more failure will terminate your access!'}
                    </p>
                    {subscription.failureCount >= 2 && (
                      <div className="space-y-2 text-sm text-error-700">
                        <p>• Review the material carefully before attempting assignments</p>
                        <p>• Take your time with each question</p>
                        <p>• Free coaching will be available if terminated</p>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* Progress Details */}
          <Card className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Detailed Progress
            </h3>

            {/* Overall Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Course Completion</span>
                <span>{subscription.progress.overallProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-primary-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${subscription.progress.overallProgress}%` }}
                />
              </div>
              {subscription.progress.isPaceBehind && (
                <div className="mt-1">
                  <div className="text-xs text-warning-600">
                    Expected: {subscription.progress.expectedProgress}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                    <div 
                      className="bg-warning-400 h-1 rounded-full"
                      style={{ width: `${subscription.progress.expectedProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Module Progress */}
            <div className="space-y-4">
              {course.modules.map((module, index) => {
                const isCompleted = subscription.progress.completedModules.includes(module._id!);
                const completedLessons = module.lessons.filter(lesson => 
                  subscription.progress.completedLessons.includes(lesson._id!)
                ).length;
                const progressPercentage = module.lessons.length > 0 
                  ? Math.round((completedLessons / module.lessons.length) * 100)
                  : 0;

                return (
                  <div key={module._id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        {isCompleted ? (
                          <CheckCircle className="h-5 w-5 text-success-600 mr-2" />
                        ) : (
                          <BookOpen className="h-5 w-5 text-gray-400 mr-2" />
                        )}
                        <h4 className="font-medium text-gray-900">
                          Module {module.order}: {module.title}
                        </h4>
                      </div>
                      <span className="text-sm text-gray-500">
                        {completedLessons}/{module.lessons.length} lessons
                      </span>
                    </div>

                    <div className="mb-2">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{progressPercentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${
                            isCompleted ? 'bg-success-600' : 'bg-primary-600'
                          }`}
                          style={{ width: `${progressPercentage}%` }}
                        />
                      </div>
                    </div>

                    {/* Lesson List */}
                    <div className="mt-3 space-y-1">
                      {module.lessons.map((lesson) => {
                        const isLessonCompleted = subscription.progress.completedLessons.includes(lesson._id!);
                        return (
                          <div key={lesson._id} className="flex items-center text-sm">
                            {isLessonCompleted ? (
                              <CheckCircle className="h-4 w-4 text-success-600 mr-2" />
                            ) : (
                              <div className="h-4 w-4 border border-gray-300 rounded-full mr-2" />
                            )}
                            <span className={isLessonCompleted ? 'text-gray-900' : 'text-gray-500'}>
                              {lesson.title}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>

          {/* Assignment Results */}
          {subscription.progress.assignmentResults.length > 0 && (
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Assignment Results
              </h3>
              <div className="space-y-4">
                {subscription.progress.assignmentResults.map((result, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">
                        Assignment {index + 1}
                      </h4>
                      <div className="flex items-center">
                        {result.passed ? (
                          <CheckCircle className="h-5 w-5 text-success-600 mr-2" />
                        ) : (
                          <XCircle className="h-5 w-5 text-error-600 mr-2" />
                        )}
                        <span className={`font-medium ${
                          result.passed ? 'text-success-600' : 'text-error-600'
                        }`}>
                          {result.percentage}%
                        </span>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      <p>Score: {result.score}/{result.totalPoints} points</p>
                      <p>Submitted: {new Date(result.submittedAt).toLocaleString()}</p>
                      <p>Status: {result.passed ? 'Passed' : 'Failed'}</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4 mt-8">
            <Link href={`/student/courses/${subscription.courseId}`}>
              <Button>
                <BookOpen className="h-4 w-4 mr-2" />
                Continue Learning
              </Button>
            </Link>
            <Link href="/student/dashboard">
              <Button variant="outline">
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}