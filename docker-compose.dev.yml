version: "3.8"

services:
  # MongoDB databases
  mongodb-auth:
    image: mongo:7.0
    container_name: timecourse-mongodb-auth
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=timecourse_auth
    volumes:
      - mongodb_auth_data:/data/db
    networks:
      - timecourse-network

  mongodb-courses:
    image: mongo:7.0
    container_name: timecourse-mongodb-courses
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_DATABASE=timecourse_courses
    volumes:
      - mongodb_courses_data:/data/db
    networks:
      - timecourse-network

  mongodb-payments:
    image: mongo:7.0
    container_name: timecourse-mongodb-payments
    ports:
      - "27019:27017"
    environment:
      - MONGO_INITDB_DATABASE=timecourse_payments
    volumes:
      - mongodb_payments_data:/data/db
    networks:
      - timecourse-network

  mongodb-analytics:
    image: mongo:7.0
    container_name: timecourse-mongodb-analytics
    ports:
      - "27020:27017"
    environment:
      - <PERSON><PERSON><PERSON><PERSON>_INITDB_DATABASE=timecourse_analytics
    volumes:
      - mongodb_analytics_data:/data/db
    networks:
      - timecourse-network

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management
    container_name: timecourse-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - timecourse-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Auth & User Service
  auth-user-service:
    build:
      context: .
      dockerfile: services/auth-user-service/Dockerfile
    container_name: timecourse-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb-auth:27017/timecourse_auth
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
    depends_on:
      - mongodb-auth
      - rabbitmq
    networks:
      - timecourse-network
    volumes:
      - ./services/auth-user-service:/app
      - /app/node_modules

  # Course & Learning Service
  course-learning-service:
    build:
      context: .
      dockerfile: services/course-learning-service/Dockerfile
    container_name: timecourse-course-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - MONGODB_URI=mongodb://mongodb-courses:27017/timecourse_courses
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
      - BUNNY_NET_API_KEY=${BUNNY_NET_API_KEY}
      - BUNNY_NET_LIBRARY_ID=${BUNNY_NET_LIBRARY_ID}
      - BUNNY_NET_STREAM_URL=${BUNNY_NET_STREAM_URL}
    depends_on:
      - mongodb-courses
      - rabbitmq
    networks:
      - timecourse-network
    volumes:
      - ./services/course-learning-service:/app
      - /app/node_modules

  # Payment & Subscription Service
  payment-subscription-service:
    build:
      context: .
      dockerfile: services/payment-subscription-service/Dockerfile
    container_name: timecourse-payment-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - MONGODB_URI=mongodb://mongodb-payments:27017/timecourse_payments
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
      - XENDIT_API_KEY=${XENDIT_API_KEY}
      - XENDIT_WEBHOOK_TOKEN=${XENDIT_WEBHOOK_TOKEN}
      - XENDIT_BASE_URL=https://api.xendit.co
      - FRONTEND_URL=http://localhost:3000
    depends_on:
      - mongodb-payments
      - rabbitmq
    networks:
      - timecourse-network
    volumes:
      - ./services/payment-subscription-service:/app
      - /app/node_modules

  # Reporting & Analytics Service
  reporting-analytics-service:
    build:
      context: .
      dockerfile: services/reporting-analytics-service/Dockerfile
    container_name: timecourse-analytics-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - MONGODB_URI=mongodb://mongodb-analytics:27017/timecourse_analytics
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
      - AUTH_SERVICE_URL=http://auth-user-service:3001
      - COURSE_SERVICE_URL=http://course-learning-service:3002
      - PAYMENT_SERVICE_URL=http://payment-subscription-service:3003
    depends_on:
      - mongodb-analytics
      - rabbitmq
    networks:
      - timecourse-network
    volumes:
      - ./services/reporting-analytics-service:/app
      - /app/node_modules

  # API Gateway (Nginx)
  api-gateway:
    image: nginx:alpine
    container_name: timecourse-api-gateway
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - auth-user-service
      - course-learning-service
      - payment-subscription-service
      - reporting-analytics-service
    networks:
      - timecourse-network

volumes:
  mongodb_auth_data:
  mongodb_courses_data:
  mongodb_payments_data:
  mongodb_analytics_data:
  rabbitmq_data:

networks:
  timecourse-network:
    driver: bridge
