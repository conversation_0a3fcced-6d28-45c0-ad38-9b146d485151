#!/usr/bin/env node

/**
 * API Endpoint Testing Script
 * Tests the TimeCourse API endpoints to verify they're properly configured
 */

const http = require('http');
const https = require('https');

// Configuration
const BASE_URL = 'http://localhost:8080';
const TIMEOUT = 5000;

// Test endpoints
const endpoints = [
  // Health checks
  { method: 'GET', path: '/health', description: 'API Gateway Health Check' },
  
  // Auth & User Service
  { method: 'POST', path: '/api/auth/register', description: 'User Registration' },
  { method: 'POST', path: '/api/auth/login', description: 'User Login' },
  { method: 'GET', path: '/api/auth/profile', description: 'Get User Profile', requiresAuth: true },
  { method: 'GET', path: '/api/users', description: 'Get All Users', requiresAuth: true },
  { method: 'GET', path: '/api/tutors', description: 'Get All Tutors', requiresAuth: true },
  
  // Course & Learning Service
  { method: 'GET', path: '/api/courses', description: 'Get Course Catalog' },
  { method: 'POST', path: '/api/courses', description: 'Create Course', requiresAuth: true },
  { method: 'GET', path: '/api/assignments', description: 'Get Assignments', requiresAuth: true },
  { method: 'GET', path: '/api/progress', description: 'Get Progress', requiresAuth: true },
  { method: 'GET', path: '/api/coaching/sessions', description: 'Get Coaching Sessions', requiresAuth: true },
  
  // Payment & Subscription Service
  { method: 'GET', path: '/api/payments', description: 'Get Payments', requiresAuth: true },
  { method: 'POST', path: '/api/payments', description: 'Create Payment', requiresAuth: true },
  { method: 'GET', path: '/api/subscriptions', description: 'Get Subscriptions', requiresAuth: true },
  
  // Analytics & Reporting Service
  { method: 'GET', path: '/api/analytics/dashboard', description: 'Get Dashboard Analytics', requiresAuth: true },
  { method: 'GET', path: '/api/reports/platform-overview', description: 'Get Platform Overview Report', requiresAuth: true }
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function makeRequest(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.path, BASE_URL);
    const options = {
      method: endpoint.method,
      timeout: TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TimeCourse-API-Tester/1.0'
      }
    };

    // Add auth header if required (dummy token for testing)
    if (endpoint.requiresAuth) {
      options.headers['Authorization'] = 'Bearer dummy-token-for-testing';
    }

    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          endpoint,
          status: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 200) // Limit response data
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        endpoint,
        status: 'ERROR',
        error: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        endpoint,
        status: 'TIMEOUT',
        error: 'Request timeout'
      });
    });

    // Add request body for POST requests
    if (endpoint.method === 'POST') {
      const sampleData = getSampleData(endpoint.path);
      if (sampleData) {
        req.write(JSON.stringify(sampleData));
      }
    }

    req.end();
  });
}

function getSampleData(path) {
  const sampleData = {
    '/api/auth/register': {
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Test User',
      role: 'student'
    },
    '/api/auth/login': {
      email: '<EMAIL>',
      password: 'password123'
    },
    '/api/courses': {
      title: 'Test Course',
      description: 'A test course',
      level: 'beginner',
      price: 99.99
    },
    '/api/payments': {
      courseId: 'test-course-id',
      amount: 99.99,
      currency: 'USD'
    }
  };
  
  return sampleData[path];
}

function getStatusColor(status) {
  if (status === 'ERROR' || status === 'TIMEOUT') return colors.red;
  if (status >= 200 && status < 300) return colors.green;
  if (status >= 300 && status < 400) return colors.yellow;
  if (status >= 400 && status < 500) return colors.yellow;
  if (status >= 500) return colors.red;
  return colors.reset;
}

function getStatusSymbol(status) {
  if (status === 'ERROR' || status === 'TIMEOUT') return '❌';
  if (status >= 200 && status < 300) return '✅';
  if (status >= 300 && status < 400) return '🔄';
  if (status >= 400 && status < 500) return '⚠️';
  if (status >= 500) return '💥';
  return '❓';
}

async function runTests() {
  console.log(`${colors.bold}${colors.blue}🚀 TimeCourse API Endpoint Testing${colors.reset}\n`);
  console.log(`Testing API at: ${colors.bold}${BASE_URL}${colors.reset}\n`);

  const results = [];
  
  for (const endpoint of endpoints) {
    process.stdout.write(`Testing ${endpoint.description}... `);
    const result = await makeRequest(endpoint);
    results.push(result);
    
    const statusColor = getStatusColor(result.status);
    const symbol = getStatusSymbol(result.status);
    
    console.log(`${symbol} ${statusColor}${result.status}${colors.reset}`);
    
    if (result.error) {
      console.log(`   ${colors.red}Error: ${result.error}${colors.reset}`);
    }
  }

  // Summary
  console.log(`\n${colors.bold}📊 Test Summary${colors.reset}`);
  console.log('='.repeat(50));
  
  const summary = {
    total: results.length,
    success: results.filter(r => r.status >= 200 && r.status < 300).length,
    clientError: results.filter(r => r.status >= 400 && r.status < 500).length,
    serverError: results.filter(r => r.status >= 500).length,
    networkError: results.filter(r => r.status === 'ERROR' || r.status === 'TIMEOUT').length
  };

  console.log(`Total Endpoints: ${summary.total}`);
  console.log(`${colors.green}✅ Success (2xx): ${summary.success}${colors.reset}`);
  console.log(`${colors.yellow}⚠️  Client Error (4xx): ${summary.clientError}${colors.reset}`);
  console.log(`${colors.red}💥 Server Error (5xx): ${summary.serverError}${colors.reset}`);
  console.log(`${colors.red}❌ Network Error: ${summary.networkError}${colors.reset}`);

  if (summary.networkError === summary.total) {
    console.log(`\n${colors.red}${colors.bold}⚠️  All endpoints failed - Services may not be running${colors.reset}`);
    console.log(`${colors.yellow}💡 Try starting the services with:${colors.reset}`);
    console.log(`   docker-compose -f docker-compose.dev.yml up --build`);
  } else if (summary.success > 0) {
    console.log(`\n${colors.green}${colors.bold}🎉 API is responding! Some endpoints are working.${colors.reset}`);
  }

  console.log(`\n${colors.blue}📋 Next Steps:${colors.reset}`);
  console.log('1. Import the Postman collection: postman-collection/TimeCourse-API-Collection.json');
  console.log('2. Start the services if not running');
  console.log('3. Test the authentication flow first');
  console.log('4. Use the collection to test complete workflows');
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, endpoints };
