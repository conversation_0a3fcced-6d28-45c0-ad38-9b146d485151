import express from "express";
import { AnalyticsController } from "../controllers/analytics.controller";
import {
  authenticateToken,
  requireRole,
} from "../../../shared/middleware/auth";

const router = express.Router();
const analyticsController = new AnalyticsController();

// Admin routes for analytics
router.get(
  "/dashboard",
  authenticateToken,
  requireRole(["admin"]),
  analyticsController.getDashboardAnalytics
);
router.get(
  "/users",
  authenticateToken,
  requireRole(["admin"]),
  analyticsController.getUserAnalytics
);
router.get(
  "/courses",
  authenticateToken,
  requireRole(["admin"]),
  analyticsController.getCourseAnalytics
);
router.get(
  "/revenue",
  authenticateToken,
  requireRole(["admin"]),
  analyticsController.getRevenueAnalytics
);
router.get(
  "/performance",
  authenticateToken,
  requireRole(["admin"]),
  analyticsController.getPerformanceAnalytics
);

// Tutor routes for their course analytics
router.get(
  "/tutor/courses",
  authenticateToken,
  requireRole(["tutor"]),
  analyticsController.getTutorCourseAnalytics
);
router.get(
  "/tutor/students",
  authenticateToken,
  requireRole(["tutor"]),
  analyticsController.getTutorStudentAnalytics
);

export default router;
