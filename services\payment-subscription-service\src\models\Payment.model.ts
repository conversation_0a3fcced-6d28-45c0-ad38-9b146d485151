import mongoose, { Document, Schema } from 'mongoose';
import { Payment, PaymentStatus } from '../../../shared/types';

export interface PaymentDocument extends Payment, Document {}

const PaymentSchema = new Schema<PaymentDocument>(
  {
    studentId: {
      type: String,
      required: true,
      index: true,
    },
    courseId: {
      type: Schema.Types.ObjectId,
      required: true,
      index: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      required: true,
      default: 'IDR',
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'cancelled'],
      required: true,
      default: 'pending',
    },
    paymentMethod: {
      type: String,
      required: true,
    },
    xenditPaymentId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    webhookData: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        delete ret.webhookData;
        return ret;
      }
    }
  }
);

// Indexes
PaymentSchema.index({ studentId: 1, status: 1 });
PaymentSchema.index({ xenditPaymentId: 1 });
PaymentSchema.index({ createdAt: -1 });

export const PaymentModel = mongoose.model<PaymentDocument>('Payment', PaymentSchema);