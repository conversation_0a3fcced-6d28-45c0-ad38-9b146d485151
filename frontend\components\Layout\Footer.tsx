import Link from 'next/link';
import { BookOpen, Mail, Phone, MapPin } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <BookOpen className="h-8 w-8 text-primary-400" />
              <span className="text-xl font-bold">Time Course</span>
            </div>
            <p className="text-gray-300 mb-4">
              Master English proficiency with our comprehensive test preparation courses. 
              Strict evaluation system with supportive coaching for guaranteed results.
            </p>
            <div className="flex space-x-4">
              <div className="flex items-center text-gray-300">
                <Mail className="h-4 w-4 mr-2" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/courses" className="text-gray-300 hover:text-white">
                  All Courses
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-300 hover:text-white">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/help" className="text-gray-300 hover:text-white">
                  Help Center
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Test Categories</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/courses?category=IELTS" className="text-gray-300 hover:text-white">
                  IELTS Preparation
                </Link>
              </li>
              <li>
                <Link href="/courses?category=TOEFL" className="text-gray-300 hover:text-white">
                  TOEFL Preparation
                </Link>
              </li>
              <li>
                <Link href="/courses?category=TOEIC" className="text-gray-300 hover:text-white">
                  TOEIC Preparation
                </Link>
              </li>
              <li>
                <Link href="/courses?category=General" className="text-gray-300 hover:text-white">
                  General English
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-300">
            © 2024 Time Course. All rights reserved. Built with dedication for English learners worldwide.
          </p>
        </div>
      </div>
    </footer>
  );
}