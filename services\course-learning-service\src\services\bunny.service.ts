import axios from 'axios';

interface VideoUploadResult {
  videoId: string;
  videoUrl: string;
  status: 'uploading' | 'processing' | 'ready' | 'failed';
}

interface VideoStatus {
  videoId: string;
  status: 'uploading' | 'processing' | 'ready' | 'failed';
  progress?: number;
  playbackUrl?: string;
  thumbnailUrl?: string;
}

export class BunnyNetService {
  private apiKey: string;
  private libraryId: string;
  private streamUrl: string;
  private baseUrl = 'https://video.bunnycdn.com';

  constructor() {
    this.apiKey = process.env.BUNNY_NET_API_KEY || '';
    this.libraryId = process.env.BUNNY_NET_LIBRARY_ID || '';
    this.streamUrl = process.env.BUNNY_NET_STREAM_URL || '';
    
    if (!this.apiKey || !this.libraryId) {
      console.warn('Bunny.net credentials not configured');
    }
  }

  async uploadVideo(file: Buffer, filename: string): Promise<VideoUploadResult> {
    try {
      if (!this.apiKey || !this.libraryId) {
        throw new Error('Bunny.net not configured');
      }

      // Create video object
      const createResponse = await axios.post(
        `${this.baseUrl}/library/${this.libraryId}/videos`,
        {
          title: filename,
        },
        {
          headers: {
            'AccessKey': this.apiKey,
            'Content-Type': 'application/json',
          },
        }
      );

      const videoId = createResponse.data.guid;

      // Upload video file
      await axios.put(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}`,
        file,
        {
          headers: {
            'AccessKey': this.apiKey,
            'Content-Type': 'application/octet-stream',
          },
        }
      );

      return {
        videoId,
        videoUrl: `${this.streamUrl}/${videoId}/playlist.m3u8`,
        status: 'uploading',
      };

    } catch (error: any) {
      console.error('Bunny.net upload error:', error.response?.data || error.message);
      throw new Error('Failed to upload video to Bunny.net');
    }
  }

  async getVideoStatus(videoId: string): Promise<VideoStatus> {
    try {
      if (!this.apiKey || !this.libraryId) {
        throw new Error('Bunny.net not configured');
      }

      const response = await axios.get(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}`,
        {
          headers: {
            'AccessKey': this.apiKey,
          },
        }
      );

      const video = response.data;
      
      return {
        videoId,
        status: this.mapBunnyStatus(video.status),
        progress: video.encodeProgress,
        playbackUrl: `${this.streamUrl}/${videoId}/playlist.m3u8`,
        thumbnailUrl: video.thumbnailFileName ? 
          `${this.streamUrl}/${videoId}/${video.thumbnailFileName}` : undefined,
      };

    } catch (error: any) {
      console.error('Bunny.net status error:', error.response?.data || error.message);
      return {
        videoId,
        status: 'failed',
      };
    }
  }

  async generateSecurePlaybackUrl(videoId: string, studentId: string): Promise<string> {
    try {
      // Generate signed URL with expiration
      const expirationTime = Math.floor(Date.now() / 1000) + (24 * 60 * 60); // 24 hours
      const token = this.generateSecurityToken(videoId, studentId, expirationTime);
      
      return `${this.streamUrl}/${videoId}/playlist.m3u8?token=${token}&expires=${expirationTime}`;

    } catch (error) {
      console.error('Error generating secure URL:', error);
      return `${this.streamUrl}/${videoId}/playlist.m3u8`;
    }
  }

  async deleteVideo(videoId: string): Promise<void> {
    try {
      if (!this.apiKey || !this.libraryId) {
        throw new Error('Bunny.net not configured');
      }

      await axios.delete(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}`,
        {
          headers: {
            'AccessKey': this.apiKey,
          },
        }
      );

    } catch (error: any) {
      console.error('Bunny.net delete error:', error.response?.data || error.message);
      throw new Error('Failed to delete video from Bunny.net');
    }
  }

  private mapBunnyStatus(bunnyStatus: number): VideoStatus['status'] {
    switch (bunnyStatus) {
      case 0: return 'uploading';
      case 1: return 'processing';
      case 2: return 'ready';
      case 3: return 'failed';
      case 4: return 'ready';
      default: return 'processing';
    }
  }

  private generateSecurityToken(videoId: string, studentId: string, expires: number): string {
    // Simple token generation - in production, use proper JWT or similar
    const data = `${videoId}-${studentId}-${expires}`;
    return Buffer.from(data).toString('base64');
  }
}