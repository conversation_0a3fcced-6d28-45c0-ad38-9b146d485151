import { Request, Response } from 'express';
import { UserModel } from '../models/User.model';
import { AuthenticatedRequest } from '../../../shared/middleware/auth';
import { ApiResponse, PaginatedResponse } from '../../../shared/types';
import <PERSON><PERSON> from 'joi';

export class TutorController {

  private createTutorSchema = Joi.object({
    firebaseUid: Joi.string().required(),
    email: Joi.string().email().required(),
    displayName: Joi.string().min(2).max(100).required(),
    photoURL: Joi.string().uri().optional(),
  });

  async createTutor(req: AuthenticatedRequest, res: Response) {
    try {
      const { error, value } = this.createTutorSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        } as ApiResponse);
      }

      const { firebaseUid, email, displayName, photoURL } = value;

      // Check if user already exists
      const existingUser = await UserModel.findOne({
        $or: [{ firebaseUid }, { email }]
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'User already exists'
        } as ApiResponse);
      }

      // Create tutor account (approved by admin)
      const tutor = new UserModel({
        firebaseUid,
        email,
        displayName,
        photoURL,
        role: 'tutor',
        isActive: true,
        approvedBy: req.user!.uid, // Admin who created the account
        approvalDate: new Date(),
      });

      await tutor.save();

      res.status(201).json({
        success: true,
        message: 'Tutor account created successfully',
        data: tutor
      } as ApiResponse);

    } catch (error: any) {
      console.error('Create tutor error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create tutor account',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getAllTutors(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string;

      const filter: any = { role: 'tutor' };
      
      if (search) {
        filter.$or = [
          { displayName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      const skip = (page - 1) * limit;
      
      const [tutors, total] = await Promise.all([
        UserModel.find(filter)
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 })
          .populate('approvedBy', 'displayName email'),
        UserModel.countDocuments(filter)
      ]);

      const response: PaginatedResponse<typeof tutors[0]> = {
        success: true,
        message: 'Tutors retrieved successfully',
        data: tutors,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      res.json(response);

    } catch (error: any) {
      console.error('Get all tutors error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve tutors',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async approveTutor(req: AuthenticatedRequest, res: Response) {
    try {
      const tutor = await UserModel.findByIdAndUpdate(
        req.params.id,
        {
          $set: {
            isActive: true,
            approvedBy: req.user!.uid,
            approvalDate: new Date()
          }
        },
        { new: true, runValidators: true }
      );

      if (!tutor || tutor.role !== 'tutor') {
        return res.status(404).json({
          success: false,
          message: 'Tutor not found'
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: 'Tutor approved successfully',
        data: tutor
      } as ApiResponse);

    } catch (error: any) {
      console.error('Approve tutor error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to approve tutor',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async deactivateTutor(req: Request, res: Response) {
    try {
      const tutor = await UserModel.findByIdAndUpdate(
        req.params.id,
        { $set: { isActive: false } },
        { new: true }
      );

      if (!tutor || tutor.role !== 'tutor') {
        return res.status(404).json({
          success: false,
          message: 'Tutor not found'
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: 'Tutor deactivated successfully'
      } as ApiResponse);

    } catch (error: any) {
      console.error('Deactivate tutor error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to deactivate tutor',
        errors: [error.message]
      } as ApiResponse);
    }
  }
}