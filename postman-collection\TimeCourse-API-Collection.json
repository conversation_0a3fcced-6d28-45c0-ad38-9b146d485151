{"info": {"name": "TimeCourse API Collection", "description": "Complete API collection for TimeCourse English Learning Platform", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "courseId", "value": "", "type": "string"}, {"key": "paymentId", "value": "", "type": "string"}], "item": [{"name": "Health Checks", "item": [{"name": "API Gateway Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}, {"name": "Auth & User Service", "item": [{"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('authToken', response.data.token);", "        pm.collectionVariables.set('userId', response.data.user.uid);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"fullName\": \"<PERSON>\",\n  \"role\": \"student\",\n  \"phoneNumber\": \"+**********\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('authToken', response.data.token);", "        pm.collectionVariables.set('userId', response.data.user.uid);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"<PERSON>\",\n  \"phoneNumber\": \"+1234567891\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}}]}, {"name": "User Management (Admin)", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}, {"name": "Get Failed Students", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/students/failed", "host": ["{{baseUrl}}"], "path": ["api", "users", "students", "failed"]}}}]}, {"name": "Tutor Management (Admin)", "item": [{"name": "Create Tutor", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"fullName\": \"<PERSON>\",\n  \"phoneNumber\": \"+1234567892\",\n  \"specialization\": \"IELTS Preparation\",\n  \"experience\": \"5 years teaching English\"\n}"}, "url": {"raw": "{{baseUrl}}/api/tutors", "host": ["{{baseUrl}}"], "path": ["api", "tutors"]}}}, {"name": "Get All Tutors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/tutors", "host": ["{{baseUrl}}"], "path": ["api", "tutors"]}}}, {"name": "Approve Tutor", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/tutors/{{userId}}/approve", "host": ["{{baseUrl}}"], "path": ["api", "tutors", "{{userId}}", "approve"]}}}]}]}, {"name": "Course & Learning Service", "item": [{"name": "Course Management", "item": [{"name": "Get Course Catalog (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/courses", "host": ["{{baseUrl}}"], "path": ["api", "courses"]}}}, {"name": "Get Course by ID", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data._id) {", "        pm.collectionVariables.set('courseId', response.data._id);", "    }", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/courses/{{courseId}}", "host": ["{{baseUrl}}"], "path": ["api", "courses", "{{courseId}}"]}}}, {"name": "Create Course (Tutor)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data._id) {", "        pm.collectionVariables.set('courseId', response.data._id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"IELTS Preparation Course\",\n  \"description\": \"Complete IELTS preparation with practice tests\",\n  \"category\": \"IELTS\",\n  \"level\": \"intermediate\",\n  \"price\": 299.99,\n  \"duration\": 60,\n  \"modules\": [\n    {\n      \"title\": \"Listening Skills\",\n      \"description\": \"Master IELTS listening section\",\n      \"order\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/courses", "host": ["{{baseUrl}}"], "path": ["api", "courses"]}}}, {"name": "Update Course (Tutor)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Advanced IELTS Preparation Course\",\n  \"description\": \"Updated course description\",\n  \"price\": 349.99\n}"}, "url": {"raw": "{{baseUrl}}/api/courses/{{courseId}}", "host": ["{{baseUrl}}"], "path": ["api", "courses", "{{courseId}}"]}}}, {"name": "Publish Course (Tutor)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/courses/{{courseId}}/publish", "host": ["{{baseUrl}}"], "path": ["api", "courses", "{{courseId}}", "publish"]}}}, {"name": "Get My Courses (<PERSON><PERSON>)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/courses/tutor/my-courses", "host": ["{{baseUrl}}"], "path": ["api", "courses", "tutor", "my-courses"]}}}]}, {"name": "Assignment Management", "item": [{"name": "Submit Assignment (Student)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignmentId\": \"assignment123\",\n  \"answers\": [\n    {\n      \"questionId\": \"q1\",\n      \"answer\": \"Sample answer\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/assignments/submit", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "submit"]}}}, {"name": "Get Assignment Result (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/assignments/assignment123/result", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "assignment123", "result"]}}}]}, {"name": "Progress Tracking", "item": [{"name": "Get My Progress (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/progress/my-progress", "host": ["{{baseUrl}}"], "path": ["api", "progress", "my-progress"]}}}, {"name": "Get Course Progress (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/progress/course/{{courseId}}", "host": ["{{baseUrl}}"], "path": ["api", "progress", "course", "{{courseId}}"]}}}]}, {"name": "Coaching Sessions", "item": [{"name": "Get Available Coaching (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/coaching/available", "host": ["{{baseUrl}}"], "path": ["api", "coaching", "available"]}}}, {"name": "Create Coaching Session (Tutor)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"IELTS Speaking Practice\",\n  \"description\": \"One-on-one speaking practice session\",\n  \"scheduledDate\": \"2024-01-15T10:00:00Z\",\n  \"duration\": 60,\n  \"maxParticipants\": 5\n}"}, "url": {"raw": "{{baseUrl}}/api/coaching", "host": ["{{baseUrl}}"], "path": ["api", "coaching"]}}}, {"name": "Get My Coaching Sessions (<PERSON><PERSON>)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/coaching/my-sessions", "host": ["{{baseUrl}}"], "path": ["api", "coaching", "my-sessions"]}}}]}]}, {"name": "Payment & Subscription Service", "item": [{"name": "Payment Management", "item": [{"name": "Create Payment (Student)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.paymentId) {", "        pm.collectionVariables.set('paymentId', response.data.paymentId);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"courseId\": \"{{courseId}}\",\n  \"amount\": 299.99,\n  \"currency\": \"USD\",\n  \"paymentMethod\": \"credit_card\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/create", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create"]}}}, {"name": "Get My Payments (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/my-payments", "host": ["{{baseUrl}}"], "path": ["api", "payments", "my-payments"]}}}, {"name": "Get Payment by ID (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/{{paymentId}}", "host": ["{{baseUrl}}"], "path": ["api", "payments", "{{paymentId}}"]}}}, {"name": "Get All Payments (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/admin/all", "host": ["{{baseUrl}}"], "path": ["api", "payments", "admin", "all"]}}}]}, {"name": "Subscription Management", "item": [{"name": "Get My Subscriptions (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/my-subscriptions", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "my-subscriptions"]}}}, {"name": "Get Subscription Progress (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/subscription123/progress", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "subscription123", "progress"]}}}, {"name": "Get All Subscriptions (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/admin/all", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "admin", "all"]}}}, {"name": "Get Failed Students (Admin/Tutor)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/admin/failed-students", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "admin", "failed-students"]}}}]}]}, {"name": "Analytics & Reporting Service", "item": [{"name": "Analytics (Admin)", "item": [{"name": "Get Dashboard Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/analytics/dashboard?period=daily&days=30", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "dashboard"], "query": [{"key": "period", "value": "daily"}, {"key": "days", "value": "30"}]}}}, {"name": "Get User Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/analytics/users?period=weekly&days=7", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "users"], "query": [{"key": "period", "value": "weekly"}, {"key": "days", "value": "7"}]}}}, {"name": "Get Course Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/analytics/courses?period=monthly&days=30", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "courses"], "query": [{"key": "period", "value": "monthly"}, {"key": "days", "value": "30"}]}}}, {"name": "Get Revenue Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/analytics/revenue?period=daily&days=30", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "revenue"], "query": [{"key": "period", "value": "daily"}, {"key": "days", "value": "30"}]}}}, {"name": "Get Performance Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/analytics/performance?period=daily&days=30", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "performance"], "query": [{"key": "period", "value": "daily"}, {"key": "days", "value": "30"}]}}}]}, {"name": "Analytics (Tu<PERSON>)", "item": [{"name": "Get Tutor Course Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/analytics/tutor/courses", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "tutor", "courses"]}}}, {"name": "Get Tutor Student Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/analytics/tutor/students", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "tutor", "students"]}}}]}, {"name": "Reports", "item": [{"name": "Get Platform Overview Report (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/reports/platform-overview?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "reports", "platform-overview"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}, {"name": "Get Course Performance Report (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/reports/course-performance?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "reports", "course-performance"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}, {"name": "Get Financial Report (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/reports/financial?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "reports", "financial"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}, {"name": "Get User Engagement Report (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/reports/user-engagement?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "reports", "user-engagement"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}, {"name": "Get Tutor Performance Report (Tu<PERSON>)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/reports/tutor/performance?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["api", "reports", "tutor", "performance"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}]}]}]}