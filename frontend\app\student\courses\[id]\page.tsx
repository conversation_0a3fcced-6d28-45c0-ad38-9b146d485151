'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import VideoPlayer from '@/components/VideoPlayer/VideoPlayer';
import AssignmentModal from '@/components/Assignment/AssignmentModal';
import { coursesAPI, subscriptionsAPI } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { Course, Subscription } from '@/types';
import { Play, BookOpen, CircleCheck as CheckCircle, Clock, TriangleAlert as AlertTriangle, FileText } from 'lucide-react';
import toast from 'react-hot-toast';

export default function StudentCoursePage() {
  const { id } = useParams();
  const { user } = useAuth();
  const [course, setCourse] = useState<Course | null>(null);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentLesson, setCurrentLesson] = useState<any>(null);
  const [showAssignment, setShowAssignment] = useState(false);
  const [currentAssignment, setCurrentAssignment] = useState<any>(null);

  useEffect(() => {
    if (id && user) {
      fetchCourseAndSubscription();
    }
  }, [id, user]);

  const fetchCourseAndSubscription = async () => {
    try {
      const [courseResponse, subscriptionsResponse] = await Promise.all([
        coursesAPI.getCourse(id as string),
        subscriptionsAPI.getMySubscriptions()
      ]);

      setCourse(courseResponse.data.data);
      
      // Find subscription for this course
      const userSubscription = subscriptionsResponse.data.data.find(
        (sub: Subscription) => sub.courseId === id
      );
      setSubscription(userSubscription);

      // Set first lesson as current if no progress
      if (courseResponse.data.data.modules.length > 0 && 
          courseResponse.data.data.modules[0].lessons.length > 0) {
        setCurrentLesson(courseResponse.data.data.modules[0].lessons[0]);
      }

    } catch (error) {
      console.error('Error fetching course data:', error);
      toast.error('Failed to load course');
    } finally {
      setLoading(false);
    }
  };

  const handleLessonSelect = (lesson: any) => {
    setCurrentLesson(lesson);
  };

  const handleAssignmentTrigger = (assignment: any) => {
    setCurrentAssignment(assignment);
    setShowAssignment(true);
  };

  const handleAssignmentSubmit = async (answers: any[]) => {
    try {
      // Submit assignment logic here
      toast.success('Assignment submitted successfully!');
      setShowAssignment(false);
      setCurrentAssignment(null);
    } catch (error) {
      toast.error('Failed to submit assignment');
    }
  };

  const isLessonCompleted = (lessonId: string) => {
    return subscription?.progress.completedLessons.includes(lessonId) || false;
  };

  const isModuleCompleted = (moduleId: string) => {
    return subscription?.progress.completedModules.includes(moduleId) || false;
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  if (!course || !subscription) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-6">
              You don't have access to this course or your subscription has expired.
            </p>
            <Button onClick={() => window.location.href = '/courses'}>
              Browse Courses
            </Button>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['student']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Course Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{course.title}</h1>
            <div className="flex items-center space-x-4">
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-1" />
                <span>
                  {Math.max(0, Math.ceil((subscription.endDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)))} days remaining
                </span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <span>Progress: {subscription.progress.overallProgress}%</span>
              </div>
              {subscription.failureCount > 0 && (
                <div className="flex items-center text-sm text-error-600">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  <span>{subscription.failureCount}/3 failures</span>
                </div>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          <Card className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Overall Progress</span>
              <span>{subscription.progress.overallProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-primary-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${subscription.progress.overallProgress}%` }}
              />
            </div>
            {subscription.progress.isPaceBehind && (
              <div className="mt-2 p-2 bg-warning-50 border border-warning-200 rounded">
                <p className="text-sm text-warning-700">
                  ⚠️ You're behind schedule! Expected progress: {subscription.progress.expectedProgress}%
                </p>
              </div>
            )}
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Course Content Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <h3 className="font-semibold text-gray-900 mb-4">Course Content</h3>
                <div className="space-y-2">
                  {course.modules.map((module) => (
                    <div key={module._id} className="border border-gray-200 rounded">
                      <div className="p-3 bg-gray-50 border-b border-gray-200">
                        <div className="flex items-center">
                          {isModuleCompleted(module._id!) ? (
                            <CheckCircle className="h-4 w-4 text-success-600 mr-2" />
                          ) : (
                            <BookOpen className="h-4 w-4 text-gray-400 mr-2" />
                          )}
                          <span className="font-medium text-sm">{module.title}</span>
                        </div>
                      </div>
                      <div className="p-2">
                        {module.lessons.map((lesson) => (
                          <button
                            key={lesson._id}
                            onClick={() => handleLessonSelect(lesson)}
                            className={`w-full text-left p-2 rounded text-sm hover:bg-gray-50 flex items-center ${
                              currentLesson?._id === lesson._id ? 'bg-primary-50 text-primary-700' : ''
                            }`}
                          >
                            {isLessonCompleted(lesson._id!) ? (
                              <CheckCircle className="h-3 w-3 text-success-600 mr-2" />
                            ) : (
                              <Play className="h-3 w-3 text-gray-400 mr-2" />
                            )}
                            <span className="truncate">{lesson.title}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            {/* Main Content Area */}
            <div className="lg:col-span-3">
              {currentLesson ? (
                <div className="space-y-6">
                  {/* Video Player */}
                  <Card>
                    <VideoPlayer
                      lesson={currentLesson}
                      onAssignmentTrigger={handleAssignmentTrigger}
                    />
                  </Card>

                  {/* Lesson Info */}
                  <Card>
                    <h2 className="text-xl font-bold text-gray-900 mb-2">
                      {currentLesson.title}
                    </h2>
                    <p className="text-gray-600 mb-4">
                      {currentLesson.description}
                    </p>
                    
                    {/* Lesson Materials */}
                    {currentLesson.materials && currentLesson.materials.length > 0 && (
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">Materials</h3>
                        <div className="space-y-2">
                          {currentLesson.materials.map((material: any) => (
                            <div key={material._id} className="flex items-center p-2 bg-gray-50 rounded">
                              <FileText className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-sm">{material.title}</span>
                              <Button size="sm" variant="outline" className="ml-auto">
                                View
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </Card>
                </div>
              ) : (
                <Card className="text-center py-12">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Select a Lesson
                  </h3>
                  <p className="text-gray-600">
                    Choose a lesson from the sidebar to start learning
                  </p>
                </Card>
              )}
            </div>
          </div>
        </div>

        {/* Assignment Modal */}
        {showAssignment && currentAssignment && (
          <AssignmentModal
            assignment={currentAssignment}
            onSubmit={handleAssignmentSubmit}
            onClose={() => setShowAssignment(false)}
          />
        )}
      </Layout>
    </ProtectedRoute>
  );
}