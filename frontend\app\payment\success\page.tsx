'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { CircleCheck as CheckCircle, BookOpen, Calendar } from 'lucide-react';

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [paymentData, setPaymentData] = useState<any>(null);

  useEffect(() => {
    // Simulate payment verification
    const timer = setTimeout(() => {
      setPaymentData({
        courseTitle: 'IELTS Preparation Course',
        amount: 'Rp 250,000',
        duration: '90 days',
        startDate: new Date().toLocaleDateString('id-ID'),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toLocaleDateString('id-ID')
      });
      setLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <Card className="text-center py-12">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600">Verifying your payment...</p>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <Card className="text-center py-12">
          <div className="mb-6">
            <CheckCircle className="h-16 w-16 text-success-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Payment Successful!
            </h1>
            <p className="text-lg text-gray-600">
              Your enrollment has been confirmed
            </p>
          </div>

          {paymentData && (
            <div className="bg-gray-50 rounded-lg p-6 mb-6 text-left">
              <h2 className="font-semibold text-gray-900 mb-4">Enrollment Details</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Course:</span>
                  <span className="font-medium">{paymentData.courseTitle}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount Paid:</span>
                  <span className="font-medium">{paymentData.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Access Duration:</span>
                  <span className="font-medium">{paymentData.duration}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Start Date:</span>
                  <span className="font-medium">{paymentData.startDate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">End Date:</span>
                  <span className="font-medium">{paymentData.endDate}</span>
                </div>
              </div>
            </div>
          )}

          <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-primary-800 mb-2">What's Next?</h3>
            <ul className="text-sm text-primary-700 space-y-1 text-left">
              <li>• Access your course immediately from your dashboard</li>
              <li>• Complete assignments to maintain progress</li>
              <li>• Remember: 3 assignment failures will terminate access</li>
              <li>• Free coaching is available if you need help</li>
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => router.push('/student/dashboard')}
              className="flex items-center"
            >
              <BookOpen className="h-4 w-4 mr-2" />
              Go to Dashboard
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/courses')}
              className="flex items-center"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Browse More Courses
            </Button>
          </div>
        </Card>
      </div>
    </Layout>
  );
}