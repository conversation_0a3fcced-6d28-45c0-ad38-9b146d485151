import axios from 'axios';
import { auth } from './firebase';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    const user = auth.currentUser;
    if (user) {
      const token = await user.getIdToken();
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

export default api;

// API endpoints
export const authAPI = {
  register: (data: any) => api.post('/api/auth/register', data),
  login: (data: any) => api.post('/api/auth/login', data),
  getProfile: () => api.get('/api/auth/profile'),
  updateProfile: (data: any) => api.put('/api/auth/profile', data),
};

export const coursesAPI = {
  getCourses: (params?: any) => api.get('/api/courses', { params }),
  getCourse: (id: string) => api.get(`/api/courses/${id}`),
  createCourse: (data: any) => api.post('/api/courses', data),
  updateCourse: (id: string, data: any) => api.put(`/api/courses/${id}`, data),
  deleteCourse: (id: string) => api.delete(`/api/courses/${id}`),
  publishCourse: (id: string) => api.put(`/api/courses/${id}/publish`),
  getTutorCourses: () => api.get('/api/courses/tutor/my-courses'),
};

export const lessonsAPI = {
  getLesson: (courseId: string, moduleId: string, lessonId: string) => 
    api.get(`/api/lessons/${courseId}/modules/${moduleId}/lessons/${lessonId}`),
  createLesson: (courseId: string, moduleId: string, data: any) => 
    api.post(`/api/lessons/${courseId}/modules/${moduleId}/lessons`, data),
  updateLesson: (courseId: string, moduleId: string, lessonId: string, data: any) => 
    api.put(`/api/lessons/${courseId}/modules/${moduleId}/lessons/${lessonId}`, data),
  uploadVideo: (courseId: string, moduleId: string, lessonId: string, file: File) => {
    const formData = new FormData();
    formData.append('video', file);
    return api.post(`/api/lessons/${courseId}/modules/${moduleId}/lessons/${lessonId}/upload-video`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },
};

export const assignmentsAPI = {
  submitAssignment: (data: any) => api.post('/api/assignments/submit', data),
  getAssignmentResult: (assignmentId: string) => api.get(`/api/assignments/${assignmentId}/result`),
  createAssignment: (courseId: string, moduleId: string, lessonId: string, data: any) => 
    api.post(`/api/assignments/${courseId}/modules/${moduleId}/lessons/${lessonId}/assignments`, data),
};

export const paymentsAPI = {
  createPayment: (data: any) => api.post('/api/payments/create', data),
  getMyPayments: (params?: any) => api.get('/api/payments/my-payments', { params }),
  getPayment: (id: string) => api.get(`/api/payments/${id}`),
};

export const subscriptionsAPI = {
  getMySubscriptions: () => api.get('/api/subscriptions/my-subscriptions'),
  getSubscription: (id: string) => api.get(`/api/subscriptions/${id}`),
  getSubscriptionProgress: (id: string) => api.get(`/api/subscriptions/${id}/progress`),
};

export const analyticsAPI = {
  getDashboard: (params?: any) => api.get('/api/analytics/dashboard', { params }),
  getUserAnalytics: (params?: any) => api.get('/api/analytics/users', { params }),
  getCourseAnalytics: (params?: any) => api.get('/api/analytics/courses', { params }),
  getCourseAnalytics: (params?: any) => api.get('/api/analytics/courses', { params }),
  getRevenueAnalytics: (params?: any) => api.get('/api/analytics/revenue', { params }),
  getPerformanceAnalytics: (params?: any) => api.get('/api/analytics/performance', { params }),
  getTutorCourseAnalytics: (params?: any) => api.get('/api/analytics/tutor/courses', { params }),
  getTutorStudentAnalytics: (params?: any) => api.get('/api/analytics/tutor/students', { params }),
};

export const usersAPI = {
  getAllUsers: (params?: any) => api.get('/api/users', { params }),
  getUser: (id: string) => api.get(`/api/users/${id}`),
  updateUser: (id: string, data: any) => api.put(`/api/users/${id}`, data),
  deleteUser: (id: string) => api.delete(`/api/users/${id}`),
};

export const tutorsAPI = {
  createTutor: (data: any) => api.post('/api/tutors', data),
  getAllTutors: (params?: any) => api.get('/api/tutors', { params }),
  approveTutor: (id: string) => api.put(`/api/tutors/${id}/approve`),
  deactivateTutor: (id: string) => api.put(`/api/tutors/${id}/deactivate`),
};

export const reportsAPI = {
  getPlatformOverview: (params?: any) => api.get('/api/reports/platform-overview', { params }),
  getCoursePerformance: (params?: any) => api.get('/api/reports/course-performance', { params }),
  getFinancialReport: (params?: any) => api.get('/api/reports/financial', { params }),
  getUserEngagement: (params?: any) => api.get('/api/reports/user-engagement', { params }),
  getTutorPerformance: (params?: any) => api.get('/api/reports/tutor/performance', { params }),
};