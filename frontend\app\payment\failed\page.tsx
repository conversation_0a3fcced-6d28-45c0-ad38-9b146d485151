'use client';

import { useRouter } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import { Circle as XCircle, RefreshCw, ArrowLeft } from 'lucide-react';

export default function PaymentFailedPage() {
  const router = useRouter();

  return (
    <Layout>
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <Card className="text-center py-12">
          <div className="mb-6">
            <XCircle className="h-16 w-16 text-error-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Payment Failed
            </h1>
            <p className="text-lg text-gray-600">
              We couldn't process your payment
            </p>
          </div>

          <div className="bg-error-50 border border-error-200 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-error-800 mb-2">Common Issues</h3>
            <ul className="text-sm text-error-700 space-y-1 text-left">
              <li>• Insufficient funds in your account</li>
              <li>• Incorrect payment details</li>
              <li>• Network connection issues</li>
              <li>• Bank security restrictions</li>
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => router.back()}
              className="flex items-center"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/courses')}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </div>

          <div className="mt-6 text-sm text-gray-600">
            <p>
              Need help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-500">
                <EMAIL>
              </a>
            </p>
          </div>
        </Card>
      </div>
    </Layout>
  );
}