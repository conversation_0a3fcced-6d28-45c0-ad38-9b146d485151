'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { coursesAPI, paymentsAPI } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { Course } from '@/types';
import { Clock, Users, Star, BookOpen, Play, CircleCheck as CheckCircle, DollarSign } from 'lucide-react';
import toast from 'react-hot-toast';

export default function CourseDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState(false);

  useEffect(() => {
    if (id) {
      fetchCourse();
    }
  }, [id]);

  const fetchCourse = async () => {
    try {
      const response = await coursesAPI.getCourse(id as string);
      setCourse(response.data.data);
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('Failed to load course details');
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    if (!course) return;

    try {
      setEnrolling(true);
      
      const paymentResponse = await paymentsAPI.createPayment({
        courseId: course._id,
        amount: course.price,
        currency: course.currency,
        paymentMethod: 'BANK_TRANSFER'
      });

      // Redirect to payment page
      window.location.href = paymentResponse.data.data.xenditPaymentUrl;

    } catch (error: any) {
      console.error('Enrollment error:', error);
      toast.error('Failed to process enrollment');
    } finally {
      setEnrolling(false);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  const formatDuration = (days: number) => {
    if (days < 30) {
      return `${days} days`;
    } else if (days < 365) {
      const months = Math.floor(days / 30);
      return `${months} month${months > 1 ? 's' : ''}`;
    } else {
      const years = Math.floor(days / 365);
      return `${years} year${years > 1 ? 's' : ''}`;
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  if (!course) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Course Not Found</h2>
            <p className="text-gray-600 mb-6">The course you're looking for doesn't exist or has been removed.</p>
            <Button onClick={() => router.push('/courses')}>
              Browse Other Courses
            </Button>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Course Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <span className="inline-block bg-primary-100 text-primary-800 text-sm px-3 py-1 rounded-full font-medium">
              {course.category}
            </span>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {course.title}
          </h1>
          
          <p className="text-xl text-gray-600 mb-6">
            {course.description}
          </p>

          {/* Course Stats */}
          <div className="flex flex-wrap items-center gap-6 text-gray-600">
            <div className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              <span>{formatDuration(course.duration)}</span>
            </div>
            <div className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              <span>{course.totalEnrollments} students</span>
            </div>
            <div className="flex items-center">
              <Star className="h-5 w-5 mr-2 text-yellow-400" />
              <span>{course.averageRating.toFixed(1)} rating</span>
            </div>
            <div className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              <span>{course.modules.length} modules</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Course Content */}
          <div className="lg:col-span-2">
            {/* Course Modules */}
            <Card className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Course Content</h2>
              
              {course.modules.length === 0 ? (
                <p className="text-gray-600">No modules available yet.</p>
              ) : (
                <div className="space-y-4">
                  {course.modules.map((module, moduleIndex) => (
                    <div key={module._id} className="border border-gray-200 rounded-lg">
                      <div className="p-4 bg-gray-50 border-b border-gray-200">
                        <h3 className="font-semibold text-gray-900">
                          Module {module.order}: {module.title}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {module.description}
                        </p>
                        <div className="flex items-center mt-2 text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>{module.estimatedDuration} minutes</span>
                          <span className="mx-2">•</span>
                          <span>{module.lessons.length} lessons</span>
                        </div>
                      </div>
                      
                      {/* Lessons */}
                      <div className="p-4">
                        <div className="space-y-2">
                          {module.lessons.map((lesson, lessonIndex) => (
                            <div key={lesson._id} className="flex items-center p-2 hover:bg-gray-50 rounded">
                              <Play className="h-4 w-4 text-gray-400 mr-3" />
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">
                                  {lesson.order}. {lesson.title}
                                </p>
                                <p className="text-sm text-gray-600">
                                  {lesson.duration} minutes
                                  {lesson.assignments.length > 0 && (
                                    <span className="ml-2 text-primary-600">
                                      • {lesson.assignments.length} assignment{lesson.assignments.length > 1 ? 's' : ''}
                                    </span>
                                  )}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card>

            {/* What You'll Learn */}
            <Card>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">What You'll Learn</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5" />
                  <span className="text-gray-700">Comprehensive {course.category} test preparation</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5" />
                  <span className="text-gray-700">Interactive assignments and quizzes</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5" />
                  <span className="text-gray-700">Real-time progress tracking</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5" />
                  <span className="text-gray-700">Certificate upon completion</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5" />
                  <span className="text-gray-700">Free coaching if needed</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5" />
                  <span className="text-gray-700">Expert tutor guidance</span>
                </div>
              </div>
            </Card>
          </div>

          {/* Enrollment Card */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {formatPrice(course.price, course.currency)}
                </div>
                <p className="text-gray-600">
                  {formatDuration(course.duration)} access
                </p>
              </div>

              {user ? (
                <Button
                  onClick={handleEnroll}
                  loading={enrolling}
                  className="w-full mb-4"
                  size="lg"
                >
                  <DollarSign className="h-5 w-5 mr-2" />
                  Enroll Now
                </Button>
              ) : (
                <Button
                  onClick={() => router.push('/auth/login')}
                  className="w-full mb-4"
                  size="lg"
                >
                  Sign In to Enroll
                </Button>
              )}

              <div className="border-t border-gray-200 pt-4">
                <h3 className="font-semibold text-gray-900 mb-3">This course includes:</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Play className="h-4 w-4 mr-2" />
                    <span>HD video lectures</span>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2" />
                    <span>Downloadable resources</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    <span>Interactive assignments</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    <span>Free coaching support</span>
                  </div>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 mr-2" />
                    <span>Certificate of completion</span>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4 mt-4">
                <div className="bg-warning-50 border border-warning-200 rounded-lg p-3">
                  <h4 className="font-medium text-warning-800 mb-1">
                    Strict Evaluation System
                  </h4>
                  <p className="text-sm text-warning-700">
                    You must pass all assignments to continue. 3 failures will terminate access, but free coaching will be available.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
}