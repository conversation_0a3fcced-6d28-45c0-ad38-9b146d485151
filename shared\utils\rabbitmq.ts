import amqp, { Connection, Channel } from "amqplib";

export class RabbitMQService {
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private readonly url: string;

  constructor(url: string = process.env.RABBITMQ_URL || "amqp://localhost") {
    this.url = url;
  }

  async connect(): Promise<void> {
    try {
      this.connection = await amqp.connect(this.url);
      this.channel = await this.connection.createChannel();

      console.log("Connected to RabbitMQ");

      // Handle connection errors
      this.connection.on("error", (err: any) => {
        console.error("RabbitMQ connection error:", err);
      });

      this.connection.on("close", () => {
        console.log("RabbitMQ connection closed");
      });
    } catch (error) {
      console.error("Failed to connect to RabbitMQ:", error);
      throw error;
    }
  }

  async publishEvent(
    exchange: string,
    routingKey: string,
    data: any
  ): Promise<void> {
    if (!this.channel) {
      throw new Error("RabbitMQ channel not available");
    }

    await this.channel.assertExchange(exchange, "topic", { durable: true });

    const message = JSON.stringify({
      ...data,
      timestamp: new Date(),
    });

    this.channel.publish(exchange, routingKey, Buffer.from(message), {
      persistent: true,
    });

    console.log(`Published event to ${exchange}.${routingKey}:`, data.type);
  }

  async subscribeToEvents(
    exchange: string,
    queue: string,
    routingKeys: string[],
    callback: (data: any) => Promise<void>
  ): Promise<void> {
    if (!this.channel) {
      throw new Error("RabbitMQ channel not available");
    }

    await this.channel.assertExchange(exchange, "topic", { durable: true });
    await this.channel.assertQueue(queue, { durable: true });

    for (const routingKey of routingKeys) {
      await this.channel.bindQueue(queue, exchange, routingKey);
    }

    await this.channel.consume(queue, async (msg: any) => {
      if (msg) {
        try {
          const data = JSON.parse(msg.content.toString());
          await callback(data);
          this.channel?.ack(msg);
        } catch (error) {
          console.error("Error processing message:", error);
          this.channel?.nack(msg, false, false);
        }
      }
    });

    console.log(
      `Subscribed to events on ${exchange}.${routingKeys.join(", ")}`
    );
  }

  async close(): Promise<void> {
    if (this.channel) {
      await this.channel.close();
    }
    if (this.connection) {
      await this.connection.close();
    }
  }
}

// Singleton instance
export const rabbitMQ = new RabbitMQService();
