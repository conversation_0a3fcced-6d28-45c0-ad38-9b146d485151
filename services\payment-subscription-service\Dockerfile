FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY services/payment-subscription-service/package*.json ./
COPY services/payment-subscription-service/tsconfig.json ./

# Copy shared dependencies to the correct relative path
COPY shared ../shared

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY services/payment-subscription-service/src ./src

# Build the application
RUN npm run build

# Remove dev dependencies to reduce image size
RUN npm prune --production

# Expose port
EXPOSE 3003

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3003/health || exit 1

# Start the service
CMD ["npm", "start"]