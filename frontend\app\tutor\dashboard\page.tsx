'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { coursesAPI } from '@/lib/api';
import { Course } from '@/types';
import { 
  BookOpen, 
  Users, 
  Plus,
  Edit,
  Eye,
  BarChart3,
  Calendar,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

export default function TutorDashboard() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const response = await coursesAPI.getTutorCourses();
      setCourses(response.data.data);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Failed to load your courses');
    } finally {
      setLoading(false);
    }
  };

  const publishedCourses = courses.filter(course => course.isPublished);
  const draftCourses = courses.filter(course => !course.isPublished);
  const totalEnrollments = courses.reduce((sum, course) => sum + course.totalEnrollments, 0);

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['tutor']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Tutor Dashboard
              </h1>
              <p className="text-lg text-gray-600">
                Manage your courses and track student progress
              </p>
            </div>
            <Link href="/tutor/courses/create">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Course
              </Button>
            </Link>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <div className="flex items-center">
                <div className="bg-primary-100 p-3 rounded-full">
                  <BookOpen className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Courses</p>
                  <p className="text-2xl font-bold text-gray-900">{courses.length}</p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-success-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Published</p>
                  <p className="text-2xl font-bold text-gray-900">{publishedCourses.length}</p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-warning-100 p-3 rounded-full">
                  <Edit className="h-6 w-6 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Drafts</p>
                  <p className="text-2xl font-bold text-gray-900">{draftCourses.length}</p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-error-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-error-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold text-gray-900">{totalEnrollments}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Actions
              </h3>
              <div className="space-y-3">
                <Link
                  href="/tutor/courses/create"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Plus className="h-5 w-5 text-primary-600 mr-3" />
                  <span className="font-medium">Create New Course</span>
                </Link>
                <Link
                  href="/tutor/coaching"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Calendar className="h-5 w-5 text-success-600 mr-3" />
                  <span className="font-medium">Manage Coaching Sessions</span>
                </Link>
                <Link
                  href="/tutor/analytics"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <BarChart3 className="h-5 w-5 text-warning-600 mr-3" />
                  <span className="font-medium">View Analytics</span>
                </Link>
                <Link
                  href="/tutor/students"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Users className="h-5 w-5 text-error-600 mr-3" />
                  <span className="font-medium">Manage Students</span>
                </Link>
              </div>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Recent Activity
              </h3>
              <div className="space-y-3">
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="bg-success-100 p-2 rounded-full mr-3">
                    <CheckCircle className="h-4 w-4 text-success-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">New enrollment</p>
                    <p className="text-sm text-gray-600">Student joined IELTS course</p>
                  </div>
                  <span className="text-sm text-gray-500">2h ago</span>
                </div>
                
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="bg-warning-100 p-2 rounded-full mr-3">
                    <Edit className="h-4 w-4 text-warning-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">Assignment submitted</p>
                    <p className="text-sm text-gray-600">Student completed Module 1 quiz</p>
                  </div>
                  <span className="text-sm text-gray-500">4h ago</span>
                </div>
                
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="bg-error-100 p-2 rounded-full mr-3">
                    <XCircle className="h-4 w-4 text-error-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">Assignment failed</p>
                    <p className="text-sm text-gray-600">Student needs coaching support</p>
                  </div>
                  <span className="text-sm text-gray-500">6h ago</span>
                </div>
              </div>
            </Card>
          </div>

          {/* Courses List */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Courses</h2>
            
            {courses.length === 0 ? (
              <Card className="text-center py-12">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Courses Yet</h3>
                <p className="text-gray-600 mb-4">
                  Create your first course to start teaching students
                </p>
                <Link href="/tutor/courses/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Course
                  </Button>
                </Link>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {courses.map((course) => (
                  <Card key={course._id} className="hover:shadow-lg transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          course.isPublished 
                            ? 'bg-success-100 text-success-800' 
                            : 'bg-warning-100 text-warning-800'
                        }`}>
                          {course.isPublished ? (
                            <>
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Published
                            </>
                          ) : (
                            <>
                              <Edit className="h-3 w-3 mr-1" />
                              Draft
                            </>
                          )}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">{course.category}</span>
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {course.title}
                    </h3>
                    
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {course.description}
                    </p>

                    {/* Course Stats */}
                    <div className="grid grid-cols-3 gap-4 mb-4 text-sm text-gray-600">
                      <div className="text-center">
                        <p className="font-medium text-gray-900">{course.modules.length}</p>
                        <p>Modules</p>
                      </div>
                      <div className="text-center">
                        <p className="font-medium text-gray-900">{course.totalEnrollments}</p>
                        <p>Students</p>
                      </div>
                      <div className="text-center">
                        <p className="font-medium text-gray-900">{course.averageRating.toFixed(1)}</p>
                        <p>Rating</p>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <Link href={`/tutor/courses/${course._id}/edit`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </Link>
                      <Link href={`/tutor/courses/${course._id}`} className="flex-1">
                        <Button size="sm" className="w-full">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </Link>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}