'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { analyticsAPI } from '@/lib/api';
import { Users, BookOpen, DollarSign, TrendingUp, TrendingDown, Calendar, ChartBar as BarChart3, Chart<PERSON><PERSON> as PieChart, Activity } from 'lucide-react';
import toast from 'react-hot-toast';

export default function AdminAnalyticsPage() {
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('daily');
  const [days, setDays] = useState(30);

  useEffect(() => {
    fetchAnalytics();
  }, [period, days]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const [dashboardResponse, userResponse, courseResponse, revenueResponse] = await Promise.all([
        analyticsAPI.getDashboard({ period, days }),
        analyticsAPI.getUserAnalytics({ period, days }),
        analyticsAPI.getCourseAnalytics({ period, days }),
        analyticsAPI.getRevenueAnalytics({ period, days })
      ]);

      setAnalytics({
        dashboard: dashboardResponse.data.data,
        users: userResponse.data.data,
        courses: courseResponse.data.data,
        revenue: revenueResponse.data.data
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Platform Analytics
              </h1>
              <p className="text-lg text-gray-600">
                Comprehensive insights into platform performance
              </p>
            </div>
            
            {/* Period Selector */}
            <div className="flex space-x-4">
              <select
                value={period}
                onChange={(e) => setPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
              
              <select
                value={days}
                onChange={(e) => setDays(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value={7}>Last 7 days</option>
                <option value={30}>Last 30 days</option>
                <option value={90}>Last 90 days</option>
                <option value={365}>Last year</option>
              </select>
            </div>
          </div>

          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <div className="flex items-center">
                <div className="bg-primary-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics?.dashboard?.overview?.totalUsers || 0}
                  </p>
                  <p className="text-xs text-success-600 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{analytics?.dashboard?.overview?.newUsers || 0} this period
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-success-100 p-3 rounded-full">
                  <BookOpen className="h-6 w-6 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Courses</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics?.dashboard?.overview?.totalCourses || 0}
                  </p>
                  <p className="text-xs text-success-600 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    {analytics?.dashboard?.overview?.totalEnrollments || 0} enrollments
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-warning-100 p-3 rounded-full">
                  <DollarSign className="h-6 w-6 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(analytics?.dashboard?.overview?.totalRevenue || 0)}
                  </p>
                  <p className="text-xs text-success-600 flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{analytics?.dashboard?.overview?.newEnrollments || 0} new
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-error-100 p-3 rounded-full">
                  <Activity className="h-6 w-6 text-error-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(analytics?.dashboard?.overview?.completionRate || 0)}
                  </p>
                  <p className="text-xs text-error-600 flex items-center">
                    <TrendingDown className="h-3 w-3 mr-1" />
                    {formatPercentage(analytics?.dashboard?.overview?.failureRate || 0)} failure
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* User Growth Chart */}
            <Card>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">User Growth</h3>
                <BarChart3 className="h-5 w-5 text-gray-400" />
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Users</span>
                  <span className="font-medium">{analytics?.users?.analytics?.length || 0} data points</span>
                </div>
                
                <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                    <p>Chart visualization would go here</p>
                    <p className="text-xs">Integration with chart library needed</p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Revenue Chart */}
            <Card>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Revenue Trends</h3>
                <TrendingUp className="h-5 w-5 text-gray-400" />
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Revenue</span>
                  <span className="font-medium">
                    {formatCurrency(analytics?.revenue?.totals?.totalRevenue || 0)}
                  </span>
                </div>
                
                <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <TrendingUp className="h-12 w-12 mx-auto mb-2" />
                    <p>Revenue chart would go here</p>
                    <p className="text-xs">Integration with chart library needed</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* User Analytics */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">User Breakdown</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Students</span>
                  <span className="font-medium">
                    {analytics?.dashboard?.overview?.totalUsers ? 
                      Math.round(analytics.dashboard.overview.totalUsers * 0.85) : 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tutors</span>
                  <span className="font-medium">
                    {analytics?.dashboard?.overview?.totalUsers ? 
                      Math.round(analytics.dashboard.overview.totalUsers * 0.12) : 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Admins</span>
                  <span className="font-medium">
                    {analytics?.dashboard?.overview?.totalUsers ? 
                      Math.round(analytics.dashboard.overview.totalUsers * 0.03) : 0}
                  </span>
                </div>
              </div>
              
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="h-32 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <PieChart className="h-8 w-8 mx-auto mb-1" />
                    <p className="text-xs">User distribution chart</p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Course Performance */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Course Performance</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Published Courses</span>
                  <span className="font-medium">
                    {analytics?.dashboard?.overview?.totalCourses || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Enrollments</span>
                  <span className="font-medium">
                    {analytics?.dashboard?.overview?.totalEnrollments || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Completion Rate</span>
                  <span className="font-medium text-success-600">
                    {formatPercentage(analytics?.dashboard?.overview?.completionRate || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Failure Rate</span>
                  <span className="font-medium text-error-600">
                    {formatPercentage(analytics?.dashboard?.overview?.failureRate || 0)}
                  </span>
                </div>
              </div>
            </Card>

            {/* Financial Summary */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Revenue</span>
                  <span className="font-medium">
                    {formatCurrency(analytics?.revenue?.totals?.totalRevenue || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Completed Payments</span>
                  <span className="font-medium text-success-600">
                    {analytics?.revenue?.totals?.completedPayments || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Failed Payments</span>
                  <span className="font-medium text-error-600">
                    {analytics?.revenue?.totals?.failedPayments || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Average Order</span>
                  <span className="font-medium">
                    {formatCurrency(250000)} {/* Mock average */}
                  </span>
                </div>
              </div>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card className="mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Platform Activity</h3>
            <div className="space-y-4">
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="bg-primary-100 p-2 rounded-full mr-4">
                  <Users className="h-4 w-4 text-primary-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">New user registrations</p>
                  <p className="text-sm text-gray-600">
                    {analytics?.dashboard?.overview?.newUsers || 0} new users in the last {days} days
                  </p>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date().toLocaleDateString()}
                </span>
              </div>
              
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="bg-success-100 p-2 rounded-full mr-4">
                  <BookOpen className="h-4 w-4 text-success-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Course enrollments</p>
                  <p className="text-sm text-gray-600">
                    {analytics?.dashboard?.overview?.newEnrollments || 0} new enrollments
                  </p>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date().toLocaleDateString()}
                </span>
              </div>
              
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="bg-warning-100 p-2 rounded-full mr-4">
                  <DollarSign className="h-4 w-4 text-warning-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Revenue generated</p>
                  <p className="text-sm text-gray-600">
                    {formatCurrency(analytics?.revenue?.totals?.totalRevenue || 0)} total revenue
                  </p>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date().toLocaleDateString()}
                </span>
              </div>
            </div>
          </Card>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}