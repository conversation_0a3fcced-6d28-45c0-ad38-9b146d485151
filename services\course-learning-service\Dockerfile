FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY services/course-learning-service/package*.json ./
COPY services/course-learning-service/tsconfig.json ./

# Copy shared dependencies to the correct relative path
COPY shared ../shared

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY services/course-learning-service/src ./src

# Build the application
RUN npm run build

# Remove dev dependencies to reduce image size
RUN npm prune --production

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3002/health || exit 1

# Start the service
CMD ["npm", "start"]