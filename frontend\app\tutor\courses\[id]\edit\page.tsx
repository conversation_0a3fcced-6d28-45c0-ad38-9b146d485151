'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import Input from '@/components/UI/Input';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { coursesAPI } from '@/lib/api';
import { Course } from '@/types';
import { ArrowLeft, Plus, CreditCard as Edit, Trash2, Save, Eye, Upload, Play } from 'lucide-react';
import toast from 'react-hot-toast';

export default function EditCoursePage() {
  const { id } = useParams();
  const router = useRouter();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    category: '',
    duration: 90,
    price: 250000,
    currency: 'IDR'
  });

  const categories = [
    'IELTS',
    'TOEFL',
    'TOEIC',
    'General English',
    'Business English',
    'Academic English',
    'Conversation English'
  ];

  useEffect(() => {
    if (id) {
      fetchCourse();
    }
  }, [id]);

  const fetchCourse = async () => {
    try {
      const response = await coursesAPI.getCourse(id as string);
      const courseData = response.data.data;
      setCourse(courseData);
      setCourseData({
        title: courseData.title,
        description: courseData.description,
        category: courseData.category,
        duration: courseData.duration,
        price: courseData.price,
        currency: courseData.currency
      });
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('Failed to load course');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveCourse = async () => {
    try {
      setSaving(true);
      await coursesAPI.updateCourse(id as string, courseData);
      toast.success('Course updated successfully');
      fetchCourse();
    } catch (error) {
      console.error('Error updating course:', error);
      toast.error('Failed to update course');
    } finally {
      setSaving(false);
    }
  };

  const handlePublishCourse = async () => {
    try {
      await coursesAPI.publishCourse(id as string);
      toast.success('Course published successfully');
      fetchCourse();
    } catch (error) {
      console.error('Error publishing course:', error);
      toast.error('Failed to publish course');
    }
  };

  const handleAddModule = () => {
    // TODO: Implement add module functionality
    toast.info('Add module functionality coming soon');
  };

  const handleAddLesson = (moduleId: string) => {
    // TODO: Implement add lesson functionality
    toast.info('Add lesson functionality coming soon');
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  if (!course) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Course Not Found</h2>
            <p className="text-gray-600 mb-6">
              The course you're looking for doesn't exist or you don't have permission to edit it.
            </p>
            <Button onClick={() => router.push('/tutor/dashboard')}>
              Back to Dashboard
            </Button>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['tutor']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="mb-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Edit Course
              </h1>
              <p className="text-lg text-gray-600">
                {course.title}
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => router.push(`/courses/${course._id}`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              {!course.isPublished && (
                <Button
                  onClick={handlePublishCourse}
                  className="bg-success-600 hover:bg-success-700"
                >
                  Publish Course
                </Button>
              )}
            </div>
          </div>

          {/* Status Banner */}
          <div className={`mb-6 p-4 rounded-lg ${
            course.isPublished 
              ? 'bg-success-50 border border-success-200' 
              : 'bg-warning-50 border border-warning-200'
          }`}>
            <p className={`font-medium ${
              course.isPublished ? 'text-success-800' : 'text-warning-800'
            }`}>
              {course.isPublished 
                ? '✅ This course is published and visible to students'
                : '⚠️ This course is in draft mode and not visible to students'
              }
            </p>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('details')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'details'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Course Details
              </button>
              <button
                onClick={() => setActiveTab('content')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'content'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Course Content
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'settings'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Settings
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'details' && (
            <Card>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Course Information
              </h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <Input
                      label="Course Title"
                      value={courseData.title}
                      onChange={(e) => setCourseData(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="e.g., IELTS Preparation Course"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      value={courseData.category}
                      onChange={(e) => setCourseData(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    >
                      {categories.map(category => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <Input
                      label="Duration (days)"
                      type="number"
                      value={courseData.duration}
                      onChange={(e) => setCourseData(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
                      min="1"
                      max="365"
                    />
                  </div>
                  
                  <div>
                    <Input
                      label="Price"
                      type="number"
                      value={courseData.price}
                      onChange={(e) => setCourseData(prev => ({ ...prev, price: parseInt(e.target.value) || 0 }))}
                      min="0"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select
                      value={courseData.currency}
                      onChange={(e) => setCourseData(prev => ({ ...prev, currency: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="IDR">IDR (Indonesian Rupiah)</option>
                      <option value="USD">USD (US Dollar)</option>
                    </select>
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={courseData.description}
                      onChange={(e) => setCourseData(prev => ({ ...prev, description: e.target.value }))}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Describe what students will learn in this course..."
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={handleSaveCourse}
                    loading={saving}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {activeTab === 'content' && (
            <div className="space-y-6">
              {/* Add Module Button */}
              <Card>
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Course Modules
                  </h2>
                  <Button onClick={handleAddModule}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Module
                  </Button>
                </div>
              </Card>

              {/* Modules List */}
              {course.modules.length === 0 ? (
                <Card className="text-center py-12">
                  <div className="text-gray-500">
                    <Play className="h-12 w-12 mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Modules Yet</h3>
                    <p className="mb-4">Start building your course by adding modules and lessons</p>
                    <Button onClick={handleAddModule}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Module
                    </Button>
                  </div>
                </Card>
              ) : (
                course.modules.map((module, moduleIndex) => (
                  <Card key={module._id}>
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Module {module.order}: {module.title}
                        </h3>
                        <p className="text-gray-600 text-sm mt-1">
                          {module.description}
                        </p>
                        <div className="flex items-center mt-2 text-sm text-gray-500">
                          <span>{module.lessons.length} lessons</span>
                          <span className="mx-2">•</span>
                          <span>{module.estimatedDuration} minutes</span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Lessons */}
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="font-medium text-gray-900">Lessons</h4>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleAddLesson(module._id!)}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Add Lesson
                        </Button>
                      </div>

                      {module.lessons.length === 0 ? (
                        <div className="text-center py-6 text-gray-500">
                          <p className="text-sm">No lessons in this module yet</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {module.lessons.map((lesson, lessonIndex) => (
                            <div key={lesson._id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                              <div className="flex items-center">
                                <Play className="h-4 w-4 text-gray-400 mr-3" />
                                <div>
                                  <p className="font-medium text-gray-900">
                                    {lesson.order}. {lesson.title}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    {lesson.duration} minutes
                                    {lesson.assignments.length > 0 && (
                                      <span className="ml-2 text-primary-600">
                                        • {lesson.assignments.length} assignment{lesson.assignments.length > 1 ? 's' : ''}
                                      </span>
                                    )}
                                  </p>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                {!lesson.videoUrl && (
                                  <Button size="sm" variant="outline">
                                    <Upload className="h-3 w-3 mr-1" />
                                    Upload Video
                                  </Button>
                                )}
                                <Button size="sm" variant="outline">
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button size="sm" variant="outline">
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </Card>
                ))
              )}
            </div>
          )}

          {activeTab === 'settings' && (
            <Card>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Course Settings
              </h2>
              
              <div className="space-y-6">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Publication Status</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    {course.isPublished 
                      ? 'Your course is currently published and visible to students.'
                      : 'Your course is in draft mode and not visible to students.'
                    }
                  </p>
                  {!course.isPublished && (
                    <Button onClick={handlePublishCourse}>
                      Publish Course
                    </Button>
                  )}
                </div>

                <div className="border border-gray-200 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Course Statistics</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Total Enrollments</p>
                      <p className="font-medium">{course.totalEnrollments}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Average Rating</p>
                      <p className="font-medium">{course.averageRating.toFixed(1)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Modules</p>
                      <p className="font-medium">{course.modules.length}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Total Lessons</p>
                      <p className="font-medium">
                        {course.modules.reduce((total, module) => total + module.lessons.length, 0)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border border-error-200 rounded-lg p-4 bg-error-50">
                  <h3 className="font-medium text-error-800 mb-2">Danger Zone</h3>
                  <p className="text-sm text-error-700 mb-4">
                    Deleting a course is permanent and cannot be undone. All student progress will be lost.
                  </p>
                  <Button variant="outline" className="border-error-300 text-error-700 hover:bg-error-100">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Course
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  );
}