'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { analyticsAPI } from '@/lib/api';
import { DashboardAnalytics } from '@/types';
import { 
  Users, 
  BookOpen, 
  DollarSign, 
  TrendingUp,
  UserCheck,
  UserX,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import toast from 'react-hot-toast';

export default function AdminDashboard() {
  const [analytics, setAnalytics] = useState<DashboardAnalytics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      const response = await analyticsAPI.getDashboard();
      setAnalytics(response.data.data.overview);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load dashboard analytics');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Admin Dashboard
            </h1>
            <p className="text-lg text-gray-600">
              Platform overview and key metrics
            </p>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <div className="flex items-center">
                <div className="bg-primary-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics?.totalUsers || 0}
                  </p>
                  <p className="text-xs text-success-600">
                    +{analytics?.newUsers || 0} this month
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-success-100 p-3 rounded-full">
                  <BookOpen className="h-6 w-6 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Courses</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics?.totalCourses || 0}
                  </p>
                  <p className="text-xs text-success-600">
                    {analytics?.totalEnrollments || 0} enrollments
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-warning-100 p-3 rounded-full">
                  <DollarSign className="h-6 w-6 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(analytics?.totalRevenue || 0)}
                  </p>
                  <p className="text-xs text-success-600">
                    +{analytics?.newEnrollments || 0} new enrollments
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-error-100 p-3 rounded-full">
                  <TrendingUp className="h-6 w-6 text-error-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics?.completionRate || 0}%
                  </p>
                  <p className="text-xs text-error-600">
                    {analytics?.failureRate || 0}% failure rate
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Actions
              </h3>
              <div className="space-y-3">
                <a
                  href="/admin/users"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <UserCheck className="h-5 w-5 text-primary-600 mr-3" />
                  <span className="font-medium">Manage Users</span>
                </a>
                <a
                  href="/admin/tutors"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Users className="h-5 w-5 text-success-600 mr-3" />
                  <span className="font-medium">Manage Tutors</span>
                </a>
                <a
                  href="/admin/courses"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <BookOpen className="h-5 w-5 text-warning-600 mr-3" />
                  <span className="font-medium">Manage Courses</span>
                </a>
                <a
                  href="/admin/analytics"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <TrendingUp className="h-5 w-5 text-error-600 mr-3" />
                  <span className="font-medium">View Analytics</span>
                </a>
              </div>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                System Status
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-success-50 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-success-600 mr-3" />
                    <span className="font-medium">API Services</span>
                  </div>
                  <span className="text-success-600 text-sm">Operational</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-success-50 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-success-600 mr-3" />
                    <span className="font-medium">Payment Gateway</span>
                  </div>
                  <span className="text-success-600 text-sm">Operational</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-success-50 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-success-600 mr-3" />
                    <span className="font-medium">Video Streaming</span>
                  </div>
                  <span className="text-success-600 text-sm">Operational</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-warning-50 rounded-lg">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-warning-600 mr-3" />
                    <span className="font-medium">Analytics Service</span>
                  </div>
                  <span className="text-warning-600 text-sm">Limited</span>
                </div>
              </div>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Platform Activity
            </h3>
            <div className="space-y-4">
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="bg-primary-100 p-2 rounded-full mr-4">
                  <UserCheck className="h-4 w-4 text-primary-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">New user registration</p>
                  <p className="text-sm text-gray-600">Student account created</p>
                </div>
                <span className="text-sm text-gray-500">2 minutes ago</span>
              </div>
              
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="bg-success-100 p-2 rounded-full mr-4">
                  <DollarSign className="h-4 w-4 text-success-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Payment completed</p>
                  <p className="text-sm text-gray-600">Course enrollment payment processed</p>
                </div>
                <span className="text-sm text-gray-500">5 minutes ago</span>
              </div>
              
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="bg-warning-100 p-2 rounded-full mr-4">
                  <AlertTriangle className="h-4 w-4 text-warning-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Assignment failure</p>
                  <p className="text-sm text-gray-600">Student failed assignment (2/3 failures)</p>
                </div>
                <span className="text-sm text-gray-500">10 minutes ago</span>
              </div>
              
              <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="bg-error-100 p-2 rounded-full mr-4">
                  <UserX className="h-4 w-4 text-error-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Subscription terminated</p>
                  <p className="text-sm text-gray-600">Student reached 3 assignment failures</p>
                </div>
                <span className="text-sm text-gray-500">15 minutes ago</span>
              </div>
            </div>
          </Card>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}