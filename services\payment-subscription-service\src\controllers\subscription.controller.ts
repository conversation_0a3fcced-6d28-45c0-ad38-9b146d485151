import { Request, Response } from 'express';
import { SubscriptionModel } from '../models/Subscription.model';
import { AuthenticatedRequest } from '../../../shared/middleware/auth';
import { ApiResponse, PaginatedResponse, SubscriptionTerminatedEvent } from '../../../shared/types';
import { rabbitMQ } from '../../../shared/utils/rabbitmq';

export class SubscriptionController {

  async getMySubscriptions(req: AuthenticatedRequest, res: Response) {
    try {
      const subscriptions = await SubscriptionModel.find({ 
        studentId: req.user!.uid 
      })
      .populate('paymentId')
      .sort({ createdAt: -1 });

      res.json({
        success: true,
        message: 'Subscriptions retrieved successfully',
        data: subscriptions
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get my subscriptions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve subscriptions',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getSubscriptionById(req: AuthenticatedRequest, res: Response) {
    try {
      const subscription = await SubscriptionModel.findOne({
        _id: req.params.subscriptionId,
        studentId: req.user!.uid
      }).populate('paymentId');

      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'Subscription not found'
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: 'Subscription retrieved successfully',
        data: subscription
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get subscription by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve subscription',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getSubscriptionProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const subscription = await SubscriptionModel.findOne({
        _id: req.params.subscriptionId,
        studentId: req.user!.uid
      });

      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'Subscription not found'
        } as ApiResponse);
      }

      // Calculate current progress and pace
      const totalModules = 10; // This should come from course service
      subscription.calculateExpectedProgress(totalModules);

      res.json({
        success: true,
        message: 'Subscription progress retrieved successfully',
        data: {
          progress: subscription.progress,
          status: subscription.status,
          failureCount: subscription.failureCount,
          daysRemaining: Math.max(0, Math.ceil((subscription.endDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get subscription progress error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve subscription progress',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getAllSubscriptions(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const status = req.query.status as string;
      
      const filter: any = {};
      if (status) {
        filter.status = status;
      }
      
      const skip = (page - 1) * limit;
      
      const [subscriptions, total] = await Promise.all([
        SubscriptionModel.find(filter)
          .populate('paymentId')
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 }),
        SubscriptionModel.countDocuments(filter)
      ]);

      const response: PaginatedResponse<typeof subscriptions[0]> = {
        success: true,
        message: 'All subscriptions retrieved successfully',
        data: subscriptions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      res.json(response);

    } catch (error: any) {
      console.error('Get all subscriptions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve subscriptions',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getFailedStudents(req: Request, res: Response) {
    try {
      const failedSubscriptions = await SubscriptionModel.find({ 
        status: 'terminated',
        failureCount: { $gte: 3 }
      })
      .populate('paymentId')
      .sort({ updatedAt: -1 });

      res.json({
        success: true,
        message: 'Failed students retrieved successfully',
        data: failedSubscriptions
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get failed students error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve failed students',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async updateSubscriptionStatus(req: Request, res: Response) {
    try {
      const { status } = req.body;
      
      if (!['active', 'expired', 'terminated'].includes(status)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid subscription status'
        } as ApiResponse);
      }

      const subscription = await SubscriptionModel.findByIdAndUpdate(
        req.params.subscriptionId,
        { $set: { status } },
        { new: true, runValidators: true }
      );

      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: 'Subscription not found'
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: 'Subscription status updated successfully',
        data: subscription
      } as ApiResponse);

    } catch (error: any) {
      console.error('Update subscription status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update subscription status',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  // Method to handle assignment completion and failure logic
  async handleAssignmentCompletion(studentId: string, courseId: string, assignmentResult: any) {
    try {
      const subscription = await SubscriptionModel.findOne({
        studentId,
        courseId,
        status: 'active'
      });

      if (!subscription) {
        console.log('No active subscription found for student:', studentId);
        return;
      }

      // Add assignment result to progress
      subscription.progress.assignmentResults.push(assignmentResult);

      // Check if assignment failed (less than 100%)
      if (!assignmentResult.passed) {
        subscription.failureCount += 1;
        
        // Check if should terminate subscription
        if (subscription.checkFailureTermination()) {
          // Publish termination event
          const terminationEvent: SubscriptionTerminatedEvent = {
            type: 'SUBSCRIPTION_TERMINATED',
            data: {
              studentId,
              courseId,
              subscriptionId: subscription._id!.toString(),
              reason: 'Failed too many assignments (3 failures)'
            },
            timestamp: new Date()
          };

          await rabbitMQ.publishEvent('subscription.events', 'subscription.terminated', terminationEvent);
          console.log(`Subscription terminated for student ${studentId} on course ${courseId}`);
        }
      }

      await subscription.save();

    } catch (error) {
      console.error('Error handling assignment completion:', error);
    }
  }
}