'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { analyticsAPI } from '@/lib/api';
import { Users, TrendingUp, TriangleAlert as AlertTriangle, CircleCheck as CheckCircle, Circle as XCircle, BookOpen, Calendar, Search, ListFilter as Filter } from 'lucide-react';
import toast from 'react-hot-toast';

export default function TutorStudentsPage() {
  const [students, setStudents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [courseFilter, setCourseFilter] = useState('');

  useEffect(() => {
    fetchStudents();
  }, [searchTerm, statusFilter, courseFilter]);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      // Mock data - in real implementation, this would fetch from analytics API
      const mockStudents = [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          course: 'IELTS Preparation',
          progress: 75,
          status: 'active',
          failureCount: 0,
          lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          enrollmentDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          isPaceBehind: false
        },
        {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          course: 'TOEFL Preparation',
          progress: 45,
          status: 'active',
          failureCount: 2,
          lastActivity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          enrollmentDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
          isPaceBehind: true
        },
        {
          id: '3',
          name: 'Mike Johnson',
          email: '<EMAIL>',
          course: 'IELTS Preparation',
          progress: 30,
          status: 'terminated',
          failureCount: 3,
          lastActivity: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          enrollmentDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
          isPaceBehind: true
        }
      ];

      // Apply filters
      let filteredStudents = mockStudents;
      
      if (searchTerm) {
        filteredStudents = filteredStudents.filter(student =>
          student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          student.email.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }
      
      if (statusFilter) {
        filteredStudents = filteredStudents.filter(student => student.status === statusFilter);
      }
      
      if (courseFilter) {
        filteredStudents = filteredStudents.filter(student => student.course === courseFilter);
      }

      setStudents(filteredStudents);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('Failed to load student data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 text-success-800';
      case 'terminated':
        return 'bg-error-100 text-error-800';
      case 'expired':
        return 'bg-warning-100 text-warning-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'terminated':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('id-ID');
  };

  const getFailureWarningColor = (count: number) => {
    if (count >= 3) return 'text-error-600';
    if (count >= 2) return 'text-warning-600';
    return 'text-gray-600';
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['tutor']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              My Students
            </h1>
            <p className="text-lg text-gray-600">
              Monitor student progress and provide support
            </p>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <div className="flex items-center">
                <div className="bg-primary-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {students.length}
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-success-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Students</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {students.filter(s => s.status === 'active').length}
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-warning-100 p-3 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">At Risk</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {students.filter(s => s.failureCount >= 2 && s.status === 'active').length}
                  </p>
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center">
                <div className="bg-error-100 p-3 rounded-full">
                  <XCircle className="h-6 w-6 text-error-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Terminated</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {students.filter(s => s.status === 'terminated').length}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search students..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="terminated">Terminated</option>
                <option value="expired">Expired</option>
              </select>

              <select
                value={courseFilter}
                onChange={(e) => setCourseFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Courses</option>
                <option value="IELTS Preparation">IELTS Preparation</option>
                <option value="TOEFL Preparation">TOEFL Preparation</option>
                <option value="TOEIC Preparation">TOEIC Preparation</option>
              </select>

              <Button variant="outline" onClick={fetchStudents}>
                <Filter className="h-4 w-4 mr-2" />
                Apply Filters
              </Button>
            </div>
          </Card>

          {/* Students List */}
          <Card>
            {students.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Students Found</h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter || courseFilter 
                    ? 'Try adjusting your search criteria'
                    : 'You don\'t have any students enrolled in your courses yet'
                  }
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Student
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Course
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Progress
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Failures
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Activity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {students.map((student) => (
                      <tr key={student.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {student.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.email}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <BookOpen className="h-4 w-4 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-900">{student.course}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-1">
                              <div className="flex justify-between text-sm mb-1">
                                <span>{student.progress}%</span>
                                {student.isPaceBehind && (
                                  <span className="text-warning-600 text-xs">Behind pace</span>
                                )}
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                  className={`h-2 rounded-full ${
                                    student.isPaceBehind ? 'bg-warning-500' : 'bg-primary-600'
                                  }`}
                                  style={{ width: `${student.progress}%` }}
                                />
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(student.status)}`}>
                            {getStatusIcon(student.status)}
                            <span className="ml-1 capitalize">{student.status}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`text-sm font-medium ${getFailureWarningColor(student.failureCount)}`}>
                            {student.failureCount}/3
                            {student.failureCount >= 2 && student.status === 'active' && (
                              <AlertTriangle className="h-3 w-3 inline ml-1" />
                            )}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(student.lastActivity)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                            {student.status === 'terminated' && (
                              <Button size="sm" variant="outline">
                                Invite to Coaching
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </Card>

          {/* Quick Actions */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Students Needing Attention
              </h3>
              <div className="space-y-3">
                {students
                  .filter(s => s.failureCount >= 2 || s.isPaceBehind)
                  .slice(0, 3)
                  .map((student) => (
                    <div key={student.id} className="flex items-center justify-between p-3 bg-warning-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{student.name}</p>
                        <p className="text-sm text-gray-600">
                          {student.failureCount >= 2 ? `${student.failureCount} failures` : 'Behind pace'}
                        </p>
                      </div>
                      <Button size="sm" variant="outline">
                        Contact
                      </Button>
                    </div>
                  ))}
                {students.filter(s => s.failureCount >= 2 || s.isPaceBehind).length === 0 && (
                  <p className="text-gray-500 text-sm">All students are doing well!</p>
                )}
              </div>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Recent Activity
              </h3>
              <div className="space-y-3">
                {students
                  .sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime())
                  .slice(0, 3)
                  .map((student) => (
                    <div key={student.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{student.name}</p>
                        <p className="text-sm text-gray-600">
                          Last active: {formatDate(student.lastActivity)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{student.progress}%</p>
                        <p className="text-xs text-gray-500">{student.course}</p>
                      </div>
                    </div>
                  ))}
              </div>
            </Card>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}