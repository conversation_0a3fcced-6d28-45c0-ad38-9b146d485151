import mongoose, { Document, Schema } from 'mongoose';
import { Course, Module, Lesson, Material, Assignment, Question } from '../../../shared/types';

export interface CourseDocument extends Course, Document {}

const QuestionSchema = new Schema<Question>({
  text: { type: String, required: true },
  type: { type: String, enum: ['short_answer', 'essay'], required: true },
  correctAnswer: { type: String }, // Only for short_answer
  points: { type: Number, required: true, default: 1 },
});

const AssignmentSchema = new Schema<Assignment>({
  title: { type: String, required: true },
  triggerTimestamp: { type: Number, required: true }, // seconds
  timeLimit: { type: Number, required: true, default: 300 }, // 5 minutes default
  questions: [QuestionSchema],
  passingScore: { type: Number, required: true, default: 100 }, // percentage
});

const MaterialSchema = new Schema<Material>({
  title: { type: String, required: true },
  type: { type: String, enum: ['pdf', 'ppt', 'document'], required: true },
  url: { type: String, required: true },
  fileSize: { type: Number, required: true },
});

const LessonSchema = new Schema<Lesson>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  order: { type: Number, required: true },
  videoUrl: { type: String },
  videoId: { type: String }, // Bunny.net video ID
  materials: [MaterialSchema],
  assignments: [AssignmentSchema],
  duration: { type: Number, required: true, default: 0 }, // minutes
});

const ModuleSchema = new Schema<Module>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  order: { type: Number, required: true },
  lessons: [LessonSchema],
  estimatedDuration: { type: Number, required: true, default: 0 }, // minutes
});

const CourseSchema = new Schema<CourseDocument>(
  {
    title: { type: String, required: true },
    description: { type: String, required: true },
    category: { type: String, required: true }, // IELTS, TOEFL, etc.
    tutorId: { type: String, required: true, index: true },
    duration: { type: Number, required: true }, // days
    price: { type: Number, required: true },
    currency: { type: String, required: true, default: 'IDR' },
    modules: [ModuleSchema],
    isPublished: { type: Boolean, default: false },
    totalEnrollments: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      }
    }
  }
);

// Indexes
CourseSchema.index({ tutorId: 1 });
CourseSchema.index({ category: 1 });
CourseSchema.index({ isPublished: 1 });
CourseSchema.index({ createdAt: -1 });
CourseSchema.index({ title: 'text', description: 'text' });

// Calculate total course duration when modules are updated
CourseSchema.pre('save', function(next) {
  if (this.modules && this.modules.length > 0) {
    const totalDuration = this.modules.reduce((total, module) => {
      const moduleDuration = module.lessons.reduce((moduleTotal, lesson) => {
        return moduleTotal + lesson.duration;
      }, 0);
      return total + moduleDuration;
    }, 0);
    
    // Update module estimated durations
    this.modules.forEach((module) => {
      module.estimatedDuration = module.lessons.reduce((total, lesson) => {
        return total + lesson.duration;
      }, 0);
    });
  }
  next();
});

export const CourseModel = mongoose.model<CourseDocument>('Course', CourseSchema);