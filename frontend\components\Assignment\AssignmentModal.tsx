'use client';

import { useState, useEffect } from 'react';
import Modal from '@/components/UI/Modal';
import Button from '@/components/UI/Button';
import Input from '@/components/UI/Input';
import { Clock, TriangleAlert as AlertTriangle } from 'lucide-react';

interface AssignmentModalProps {
  assignment: any;
  onSubmit: (answers: any[]) => void;
  onClose: () => void;
}

export default function AssignmentModal({ assignment, onSubmit, onClose }: AssignmentModalProps) {
  const [answers, setAnswers] = useState<{ [key: string]: string }>({});
  const [timeLeft, setTimeLeft] = useState(assignment.timeLimit);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev: number) => {
        if (prev <= 1) {
          handleAutoSubmit();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleAutoSubmit = () => {
    const formattedAnswers = assignment.questions.map((question: any) => ({
      questionId: question._id,
      answer: answers[question._id] || ''
    }));
    onSubmit(formattedAnswers);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    const formattedAnswers = assignment.questions.map((question: any) => ({
      questionId: question._id,
      answer: answers[question._id] || ''
    }));

    onSubmit(formattedAnswers);
  };

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isTimeRunningOut = timeLeft <= 30;

  return (
    <Modal
      isOpen={true}
      onClose={() => {}} // Prevent closing during assignment
      title={assignment.title}
      size="lg"
    >
      <div className="space-y-6">
        {/* Timer */}
        <div className={`flex items-center justify-center p-4 rounded-lg ${
          isTimeRunningOut ? 'bg-error-50 border border-error-200' : 'bg-primary-50 border border-primary-200'
        }`}>
          <Clock className={`h-5 w-5 mr-2 ${isTimeRunningOut ? 'text-error-600' : 'text-primary-600'}`} />
          <span className={`font-semibold ${isTimeRunningOut ? 'text-error-700' : 'text-primary-700'}`}>
            Time Remaining: {formatTime(timeLeft)}
          </span>
        </div>

        {/* Warning */}
        <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-warning-600 mr-2 mt-0.5" />
            <div>
              <h4 className="font-medium text-warning-800">Important Notice</h4>
              <p className="text-sm text-warning-700 mt-1">
                You must score 100% to pass this assignment. Failing 3 assignments will terminate your course access.
              </p>
            </div>
          </div>
        </div>

        {/* Questions */}
        <div className="space-y-6">
          {assignment.questions.map((question: any, index: number) => (
            <div key={question._id} className="border border-gray-200 rounded-lg p-4">
              <div className="mb-3">
                <h3 className="font-medium text-gray-900 mb-2">
                  Question {index + 1} ({question.points} point{question.points > 1 ? 's' : ''})
                </h3>
                <p className="text-gray-700">{question.text}</p>
              </div>

              {question.type === 'short_answer' ? (
                <Input
                  placeholder="Enter your answer..."
                  value={answers[question._id] || ''}
                  onChange={(e) => handleAnswerChange(question._id, e.target.value)}
                  disabled={isSubmitting}
                />
              ) : (
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  rows={4}
                  placeholder="Write your essay answer..."
                  value={answers[question._id] || ''}
                  onChange={(e) => handleAnswerChange(question._id, e.target.value)}
                  disabled={isSubmitting}
                />
              )}
            </div>
          ))}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <Button
            onClick={handleSubmit}
            loading={isSubmitting}
            disabled={timeLeft === 0}
            className="min-w-32"
          >
            Submit Assignment
          </Button>
        </div>

        {/* Progress */}
        <div className="text-center text-sm text-gray-600">
          <p>
            Passing Score: {assignment.passingScore}% • 
            Questions Answered: {Object.keys(answers).length}/{assignment.questions.length}
          </p>
        </div>
      </div>
    </Modal>
  );
}