# TimeCourse API Postman Collection

This Postman collection provides comprehensive testing capabilities for the TimeCourse English Learning Platform API.

## 🚀 Quick Start

### 1. Import the Collection
1. Open Postman
2. Click "Import" button
3. Select `TimeCourse-API-Collection.json`
4. The collection will be imported with all endpoints organized by service

### 2. Set Up Environment Variables
The collection uses the following variables that are automatically managed:

- `baseUrl`: API Gateway URL (default: `http://localhost:8080`)
- `authToken`: JWT token (automatically set after login)
- `userId`: Current user ID (automatically set after login/register)
- `courseId`: Course ID (automatically set when creating/fetching courses)
- `paymentId`: Payment ID (automatically set when creating payments)

### 3. Start the Services
Make sure all services are running:
```bash
# Start all services with Docker Compose
docker-compose -f docker-compose.dev.yml up --build

# Or start individual services for development
npm run dev
```

## 📋 Testing Workflow

### For Students:
1. **Register/Login** → Use "Auth & User Service" → "Authentication"
2. **Browse Courses** → Use "Course & Learning Service" → "Course Management" → "Get Course Catalog"
3. **Create Payment** → Use "Payment & Subscription Service" → "Payment Management"
4. **Submit Assignments** → Use "Course & Learning Service" → "Assignment Management"
5. **Track Progress** → Use "Course & Learning Service" → "Progress Tracking"
6. **Join Coaching** → Use "Course & Learning Service" → "Coaching Sessions"

### For Tutors:
1. **Login** → Use "Auth & User Service" → "Authentication"
2. **Create Courses** → Use "Course & Learning Service" → "Course Management"
3. **Manage Coaching** → Use "Course & Learning Service" → "Coaching Sessions"
4. **View Analytics** → Use "Analytics & Reporting Service" → "Analytics (Tutor)"

### For Admins:
1. **Login** → Use "Auth & User Service" → "Authentication"
2. **Manage Users** → Use "Auth & User Service" → "User Management"
3. **Manage Tutors** → Use "Auth & User Service" → "Tutor Management"
4. **View Analytics** → Use "Analytics & Reporting Service" → "Analytics (Admin)"
5. **Generate Reports** → Use "Analytics & Reporting Service" → "Reports"

## 🔐 Authentication

The collection automatically handles JWT tokens:

1. **Register or Login** using the authentication endpoints
2. The `authToken` variable is automatically set from the response
3. All subsequent requests use this token in the Authorization header
4. Token is valid for the session duration

### Sample User Accounts for Testing:

**Student Account:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "John Doe",
  "role": "student"
}
```

**Tutor Account:**
```json
{
  "email": "<EMAIL>", 
  "password": "password123",
  "fullName": "Jane Smith",
  "role": "tutor"
}
```

**Admin Account:**
```json
{
  "email": "<EMAIL>",
  "password": "password123", 
  "fullName": "Admin User",
  "role": "admin"
}
```

## 📊 Service Endpoints Overview

### Auth & User Service (Port 3001)
- **Authentication**: Register, Login, Profile management
- **User Management**: Admin user operations
- **Tutor Management**: Admin tutor operations

### Course & Learning Service (Port 3002)
- **Course Management**: CRUD operations for courses
- **Assignment Management**: Submit and track assignments
- **Progress Tracking**: Student progress monitoring
- **Coaching Sessions**: Tutor-student coaching

### Payment & Subscription Service (Port 3003)
- **Payment Management**: Create and track payments
- **Subscription Management**: Manage course subscriptions
- **Webhook Handling**: Payment provider webhooks

### Analytics & Reporting Service (Port 3004)
- **Analytics**: Dashboard, user, course, revenue, performance metrics
- **Reports**: Platform overview, financial, engagement reports

## 🔧 Configuration

### Environment Variables
You can customize the base URL by modifying the collection variables:

1. Click on the collection name
2. Go to "Variables" tab
3. Update `baseUrl` to match your deployment

### API Gateway
All requests go through the Nginx API Gateway at port 8080, which routes to:
- `/api/auth/*`, `/api/users/*`, `/api/tutors/*` → Auth Service (3001)
- `/api/courses/*`, `/api/lessons/*`, `/api/assignments/*`, `/api/progress/*`, `/api/coaching/*` → Course Service (3002)
- `/api/payments/*`, `/api/subscriptions/*`, `/api/webhooks/*` → Payment Service (3003)
- `/api/analytics/*`, `/api/reports/*` → Analytics Service (3004)

## 🧪 Testing Tips

1. **Start with Health Checks** to ensure all services are running
2. **Use the Authentication flow** first to get valid tokens
3. **Follow the logical workflow** (register → create course → make payment → etc.)
4. **Check Response Status** and use the test scripts to auto-populate variables
5. **Use Query Parameters** for filtering analytics and reports
6. **Test Different User Roles** to verify authorization

## 🐛 Troubleshooting

### Common Issues:

**401 Unauthorized:**
- Make sure you're logged in and the `authToken` is set
- Check if the token has expired

**404 Not Found:**
- Verify the service is running
- Check the endpoint URL matches the route definitions

**500 Internal Server Error:**
- Check service logs: `docker-compose logs -f [service-name]`
- Verify database connections
- Ensure RabbitMQ is running

**Connection Refused:**
- Make sure Docker services are running
- Check port mappings in docker-compose.yml
- Verify API Gateway (Nginx) is routing correctly

### Service Health Checks:
- API Gateway: `GET /health`
- Individual services: Check Docker logs for startup messages

## 📝 Notes

- The collection includes automatic token management
- Variables are automatically populated from successful responses
- Query parameters are included for analytics endpoints
- All endpoints include proper authorization headers
- Sample request bodies are provided for POST/PUT requests

Happy testing! 🚀
