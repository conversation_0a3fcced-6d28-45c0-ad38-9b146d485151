// Shared Types across all microservices
export interface User {
  _id?: string;
  firebaseUid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Tutor specific fields
  approvedBy?: string; // Admin ID who approved the tutor
  approvalDate?: Date;
  // Student specific fields
  subscriptions?: string[]; // Subscription IDs
}

export type UserRole = 'student' | 'tutor' | 'admin';

export interface Course {
  _id?: string;
  title: string;
  description: string;
  category: string; // IELTS, TOEFL, TOEIC, etc.
  tutorId: string;
  duration: number; // in days
  price: number;
  currency: string;
  modules: Module[];
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
  totalEnrollments: number;
  averageRating: number;
}

export interface Module {
  _id?: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  estimatedDuration: number; // in minutes
}

export interface Lesson {
  _id?: string;
  title: string;
  description: string;
  order: number;
  videoUrl?: string; // Bunny.net URL
  videoId?: string; // Bunny.net video ID
  materials: Material[];
  assignments: Assignment[];
  duration: number; // in minutes
}

export interface Material {
  _id?: string;
  title: string;
  type: 'pdf' | 'ppt' | 'document';
  url: string;
  fileSize: number;
}

export interface Assignment {
  _id?: string;
  title: string;
  triggerTimestamp: number; // seconds from video start
  timeLimit: number; // seconds
  questions: Question[];
  passingScore: number; // percentage (default 100%)
}

export interface Question {
  _id?: string;
  text: string;
  type: 'short_answer' | 'essay';
  correctAnswer?: string; // for short answer
  points: number;
}

export interface Subscription {
  _id?: string;
  studentId: string;
  courseId: string;
  status: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  failureCount: number;
  progress: Progress;
  paymentId: string;
  createdAt: Date;
  updatedAt: Date;
}

export type SubscriptionStatus = 'active' | 'expired' | 'terminated';

export interface Progress {
  completedModules: string[];
  completedLessons: string[];
  assignmentResults: AssignmentResult[];
  overallProgress: number; // percentage
  isPaceBehind: boolean;
  expectedProgress: number; // based on days passed
}

export interface AssignmentResult {
  assignmentId: string;
  lessonId: string;
  score: number;
  totalPoints: number;
  percentage: number;
  passed: boolean;
  submittedAt: Date;
  answers: StudentAnswer[];
}

export interface StudentAnswer {
  questionId: string;
  answer: string;
  isCorrect: boolean;
  points: number;
}

export interface Payment {
  _id?: string;
  studentId: string;
  courseId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string;
  xenditPaymentId: string;
  webhookData?: any;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'cancelled';

export interface CoachingSession {
  _id?: string;
  tutorId: string;
  courseId: string;
  title: string;
  description: string;
  scheduledDate: Date;
  meetingLink: string;
  maxParticipants: number;
  enrolledStudents: string[]; // Student IDs
  createdAt: Date;
  status: 'scheduled' | 'completed' | 'cancelled';
}

export interface Certificate {
  _id?: string;
  studentId: string;
  courseId: string;
  verificationCode: string;
  completionDate: Date;
  certificateUrl: string;
  isValid: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Event Types for RabbitMQ
export interface UserCreatedEvent {
  type: 'USER_CREATED';
  data: User;
  timestamp: Date;
}

export interface CourseEnrollmentEvent {
  type: 'COURSE_ENROLLED';
  data: {
    studentId: string;
    courseId: string;
    subscriptionId: string;
  };
  timestamp: Date;
}

export interface AssignmentCompletedEvent {
  type: 'ASSIGNMENT_COMPLETED';
  data: {
    studentId: string;
    courseId: string;
    assignmentId: string;
    result: AssignmentResult;
  };
  timestamp: Date;
}

export interface SubscriptionTerminatedEvent {
  type: 'SUBSCRIPTION_TERMINATED';
  data: {
    studentId: string;
    courseId: string;
    subscriptionId: string;
    reason: string;
  };
  timestamp: Date;
}

export interface PaymentCompletedEvent {
  type: 'PAYMENT_COMPLETED';
  data: {
    paymentId: string;
    studentId: string;
    courseId: string;
    amount: number;
  };
  timestamp: Date;
}