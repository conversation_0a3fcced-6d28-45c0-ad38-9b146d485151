'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import Layout from '@/components/Layout/Layout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { subscriptionsAPI } from '@/lib/api';
import { Subscription } from '@/types';
import { 
  BookOpen, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Calendar,
  TrendingUp,
  Users
} from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

export default function StudentDashboard() {
  const { user } = useAuth();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchSubscriptions();
    }
  }, [user]);

  const fetchSubscriptions = async () => {
    try {
      const response = await subscriptionsAPI.getMySubscriptions();
      setSubscriptions(response.data.data);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      toast.error('Failed to load your courses');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 text-success-800';
      case 'expired':
        return 'bg-warning-100 text-warning-800';
      case 'terminated':
        return 'bg-error-100 text-error-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'expired':
        return <Clock className="h-4 w-4" />;
      case 'terminated':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
  const terminatedSubscriptions = subscriptions.filter(sub => sub.status === 'terminated');

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.displayName}!
          </h1>
          <p className="text-lg text-gray-600">
            Track your progress and continue your English learning journey
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <div className="flex items-center">
              <div className="bg-primary-100 p-3 rounded-full">
                <BookOpen className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Courses</p>
                <p className="text-2xl font-bold text-gray-900">{activeSubscriptions.length}</p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="bg-success-100 p-3 rounded-full">
                <TrendingUp className="h-6 w-6 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg. Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {activeSubscriptions.length > 0 
                    ? Math.round(activeSubscriptions.reduce((acc, sub) => acc + sub.progress.overallProgress, 0) / activeSubscriptions.length)
                    : 0}%
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="bg-warning-100 p-3 rounded-full">
                <AlertTriangle className="h-6 w-6 text-warning-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Behind Pace</p>
                <p className="text-2xl font-bold text-gray-900">
                  {activeSubscriptions.filter(sub => sub.progress.isPaceBehind).length}
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="bg-error-100 p-3 rounded-full">
                <XCircle className="h-6 w-6 text-error-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Terminated</p>
                <p className="text-2xl font-bold text-gray-900">{terminatedSubscriptions.length}</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Pace Warnings */}
        {activeSubscriptions.some(sub => sub.progress.isPaceBehind) && (
          <Card className="mb-8 border-l-4 border-warning-500 bg-warning-50">
            <div className="flex items-start">
              <AlertTriangle className="h-6 w-6 text-warning-600 mt-1" />
              <div className="ml-3">
                <h3 className="text-lg font-medium text-warning-800 mb-2">
                  You're Behind Schedule!
                </h3>
                <p className="text-warning-700 mb-4">
                  Some of your courses are behind the recommended pace. Consider dedicating more time to catch up.
                </p>
                <div className="space-y-2">
                  {activeSubscriptions
                    .filter(sub => sub.progress.isPaceBehind)
                    .map((sub) => (
                      <div key={sub._id} className="text-sm text-warning-700">
                        Course progress: {sub.progress.overallProgress}% (Expected: {sub.progress.expectedProgress}%)
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Free Coaching Invitations */}
        {terminatedSubscriptions.length > 0 && (
          <Card className="mb-8 border-l-4 border-primary-500 bg-primary-50">
            <div className="flex items-start">
              <Users className="h-6 w-6 text-primary-600 mt-1" />
              <div className="ml-3">
                <h3 className="text-lg font-medium text-primary-800 mb-2">
                  Free Coaching Available
                </h3>
                <p className="text-primary-700 mb-4">
                  You're eligible for free group coaching sessions to help you improve and restart your learning journey.
                </p>
                <Link href="/student/coaching">
                  <Button variant="outline" size="sm">
                    View Available Sessions
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        )}

        {/* Active Courses */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Active Courses</h2>
          
          {activeSubscriptions.length === 0 ? (
            <Card className="text-center py-12">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Courses</h3>
              <p className="text-gray-600 mb-4">
                Start your English learning journey by enrolling in a course
              </p>
              <Link href="/courses">
                <Button>Browse Courses</Button>
              </Link>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {activeSubscriptions.map((subscription) => (
                <Card key={subscription._id} className="hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                        {getStatusIcon(subscription.status)}
                        <span className="ml-1 capitalize">{subscription.status}</span>
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {getDaysRemaining(subscription.endDate.toString())} days left
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Course ID: {subscription.courseId}
                  </h3>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>{subscription.progress.overallProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${subscription.progress.overallProgress}%` }}
                      />
                    </div>
                    {subscription.progress.isPaceBehind && (
                      <p className="text-xs text-warning-600 mt-1">
                        Expected: {subscription.progress.expectedProgress}%
                      </p>
                    )}
                  </div>

                  {/* Failure Count Warning */}
                  {subscription.failureCount > 0 && (
                    <div className="mb-4 p-3 bg-error-50 border border-error-200 rounded-md">
                      <div className="flex items-center">
                        <AlertTriangle className="h-4 w-4 text-error-600 mr-2" />
                        <span className="text-sm text-error-700">
                          {subscription.failureCount}/3 failures
                          {subscription.failureCount >= 2 && ' - Be careful!'}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Course Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Completed Modules:</span>
                      <span className="ml-1">{subscription.progress.completedModules.length}</span>
                    </div>
                    <div>
                      <span className="font-medium">Completed Lessons:</span>
                      <span className="ml-1">{subscription.progress.completedLessons.length}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <Link href={`/student/courses/${subscription.courseId}`} className="flex-1">
                      <Button className="w-full" size="sm">
                        Continue Learning
                      </Button>
                    </Link>
                    <Link href={`/student/progress/${subscription._id}`}>
                      <Button variant="outline" size="sm">
                        View Progress
                      </Button>
                    </Link>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Course History */}
        {subscriptions.filter(sub => sub.status !== 'active').length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Course History</h2>
            <Card>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Course
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Progress
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        End Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {subscriptions
                      .filter(sub => sub.status !== 'active')
                      .map((subscription) => (
                        <tr key={subscription._id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Course ID: {subscription.courseId}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                              {getStatusIcon(subscription.status)}
                              <span className="ml-1 capitalize">{subscription.status}</span>
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {subscription.progress.overallProgress}%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(subscription.endDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {subscription.status === 'terminated' && (
                              <Link href="/student/coaching" className="text-primary-600 hover:text-primary-900">
                                Get Coaching
                              </Link>
                            )}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  );
}