'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import Input from '@/components/UI/Input';
import { coursesAPI } from '@/lib/api';
import { BookOpen, Plus, ArrowLeft } from 'lucide-react';
import toast from 'react-hot-toast';

export default function CreateCoursePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    duration: 90,
    price: 250000,
    currency: 'IDR'
  });

  const categories = [
    'IELTS',
    'TOEFL',
    'TOEIC',
    'General English',
    'Business English',
    'Academic English',
    'Conversation English'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'duration' || name === 'price' ? parseInt(value) || 0 : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.description || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      
      const response = await coursesAPI.createCourse(formData);
      
      toast.success('Course created successfully!');
      router.push(`/tutor/courses/${response.data.data._id}/edit`);
      
    } catch (error: any) {
      console.error('Error creating course:', error);
      toast.error('Failed to create course');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute allowedRoles={['tutor']}>
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Create New Course
            </h1>
            <p className="text-lg text-gray-600">
              Set up your course details and structure
            </p>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Basic Information
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <Input
                      label="Course Title *"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="e.g., IELTS Preparation Course"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category *
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      required
                    >
                      <option value="">Select a category</option>
                      {categories.map(category => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <Input
                      label="Duration (days) *"
                      name="duration"
                      type="number"
                      value={formData.duration}
                      onChange={handleInputChange}
                      min="1"
                      max="365"
                      required
                    />
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description *
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Describe what students will learn in this course..."
                      required
                    />
                  </div>
                </div>
              </Card>

              {/* Pricing */}
              <Card>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Pricing
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Input
                      label="Price *"
                      name="price"
                      type="number"
                      value={formData.price}
                      onChange={handleInputChange}
                      min="0"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select
                      name="currency"
                      value={formData.currency}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="IDR">IDR (Indonesian Rupiah)</option>
                      <option value="USD">USD (US Dollar)</option>
                    </select>
                  </div>
                </div>
                
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">
                    <strong>Preview:</strong> Students will pay{' '}
                    {new Intl.NumberFormat('id-ID', {
                      style: 'currency',
                      currency: formData.currency
                    }).format(formData.price)}{' '}
                    for {formData.duration} days of access.
                  </p>
                </div>
              </Card>

              {/* Important Notes */}
              <Card>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Important Notes
                </h2>
                
                <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
                  <h3 className="font-medium text-warning-800 mb-2">
                    Course Requirements
                  </h3>
                  <ul className="text-sm text-warning-700 space-y-1">
                    <li>• After creating the course, you'll need to add modules and lessons</li>
                    <li>• Each lesson can have video content and assignments</li>
                    <li>• Assignments must be passed with 100% score</li>
                    <li>• Students get terminated after 3 assignment failures</li>
                    <li>• Course must be published before students can enroll</li>
                  </ul>
                </div>
              </Card>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  loading={loading}
                  className="min-w-32"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Course
                </Button>
              </div>
            </div>
          </form>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}