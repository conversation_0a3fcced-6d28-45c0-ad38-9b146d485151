'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { Users, Calendar, Clock, Video, CircleCheck as CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';

export default function StudentCoachingPage() {
  const [coachingSessions, setCoachingSessions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [joiningSession, setJoiningSession] = useState<string | null>(null);

  useEffect(() => {
    fetchCoachingSessions();
  }, []);

  const fetchCoachingSessions = async () => {
    try {
      // Mock data - replace with actual API call
      const mockSessions = [
        {
          _id: '1',
          title: 'IELTS Writing Task 1 - Remedial Session',
          description: 'Focus on graph description and data interpretation techniques',
          tutorName: '<PERSON>',
          scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          meetingLink: 'https://zoom.us/j/123456789',
          maxParticipants: 10,
          enrolledCount: 7,
          courseCategory: 'IELTS',
          status: 'scheduled'
        },
        {
          _id: '2',
          title: 'TOEFL Speaking Practice - Group Session',
          description: 'Practice speaking tasks with peer feedback and tutor guidance',
          tutorName: '<PERSON>',
          scheduledDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          meetingLink: 'https://meet.google.com/abc-defg-hij',
          maxParticipants: 8,
          enrolledCount: 5,
          courseCategory: 'TOEFL',
          status: 'scheduled'
        }
      ];
      
      setCoachingSessions(mockSessions);
    } catch (error) {
      console.error('Error fetching coaching sessions:', error);
      toast.error('Failed to load coaching sessions');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinSession = async (sessionId: string) => {
    try {
      setJoiningSession(sessionId);
      
      // Mock API call to join session
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Successfully joined coaching session!');
      
      // In real implementation, this would update the session data
      setCoachingSessions(prev => 
        prev.map(session => 
          session._id === sessionId 
            ? { ...session, enrolledCount: session.enrolledCount + 1 }
            : session
        )
      );
      
    } catch (error) {
      console.error('Error joining session:', error);
      toast.error('Failed to join coaching session');
    } finally {
      setJoiningSession(null);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['student']}>
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Free Coaching Sessions
            </h1>
            <p className="text-lg text-gray-600">
              Join group coaching sessions to improve your skills and get back on track
            </p>
          </div>

          {/* Info Card */}
          <Card className="mb-8 border-l-4 border-primary-500 bg-primary-50">
            <div className="flex items-start">
              <Users className="h-6 w-6 text-primary-600 mt-1" />
              <div className="ml-3">
                <h3 className="text-lg font-medium text-primary-800 mb-2">
                  About Free Coaching
                </h3>
                <div className="text-primary-700 space-y-1">
                  <p>• Available for students whose course access was terminated due to assignment failures</p>
                  <p>• Group sessions led by experienced tutors</p>
                  <p>• Focus on areas where you need improvement</p>
                  <p>• Completely free - no additional charges</p>
                  <p>• Limited seats available - join early!</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Coaching Sessions */}
          {coachingSessions.length === 0 ? (
            <Card className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Coaching Sessions Available
              </h3>
              <p className="text-gray-600 mb-4">
                There are currently no coaching sessions scheduled. Check back later or contact support.
              </p>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {coachingSessions.map((session) => (
                <Card key={session._id} className="hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {session.status === 'scheduled' ? 'Available' : session.status}
                      </span>
                    </div>
                    <span className="text-sm text-gray-500">{session.courseCategory}</span>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {session.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-4">
                    {session.description}
                  </p>

                  {/* Session Details */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-2" />
                      <span>Tutor: {session.tutorName}</span>
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{formatDate(session.scheduledDate)}</span>
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-2" />
                      <span>
                        {session.enrolledCount}/{session.maxParticipants} participants
                      </span>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Enrollment</span>
                      <span>{Math.round((session.enrolledCount / session.maxParticipants) * 100)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(session.enrolledCount / session.maxParticipants) * 100}%` }}
                      />
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <Button
                      onClick={() => handleJoinSession(session._id)}
                      loading={joiningSession === session._id}
                      disabled={session.enrolledCount >= session.maxParticipants}
                      className="flex-1"
                      size="sm"
                    >
                      <Users className="h-4 w-4 mr-1" />
                      {session.enrolledCount >= session.maxParticipants ? 'Full' : 'Join Session'}
                    </Button>
                    
                    {session.meetingLink && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(session.meetingLink, '_blank')}
                        className="flex items-center"
                      >
                        <Video className="h-4 w-4 mr-1" />
                        Meeting Link
                      </Button>
                    )}
                  </div>

                  {/* Session Status */}
                  {session.enrolledCount >= session.maxParticipants && (
                    <div className="mt-3 p-2 bg-warning-50 border border-warning-200 rounded">
                      <p className="text-sm text-warning-700">
                        This session is full. More sessions may be added based on demand.
                      </p>
                    </div>
                  )}
                </Card>
              ))}
            </div>
          )}

          {/* Help Section */}
          <Card className="mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Need More Help?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Contact Support</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Our support team is here to help you get back on track.
                </p>
                <Button variant="outline" size="sm">
                  Contact Support
                </Button>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Study Resources</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Access additional study materials and practice tests.
                </p>
                <Button variant="outline" size="sm">
                  View Resources
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}