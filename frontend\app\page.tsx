import Link from 'next/link';
import Layout from '@/components/Layout/Layout';
import { Book<PERSON><PERSON>, Users, Award, Clock, CheckCircle, Star } from 'lucide-react';

export default function HomePage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Master English with
              <span className="block text-primary-200">Time Course</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
              Comprehensive test preparation with strict evaluation system and supportive coaching. 
              Your success is our commitment.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/courses"
                className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors"
              >
                Explore Courses
              </Link>
              <Link
                href="/auth/login"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Time Course?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our unique approach combines rigorous evaluation with supportive coaching 
              to ensure your English proficiency success.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Strict Progress Enforcement
              </h3>
              <p className="text-gray-600">
                Pass integrated assignments to continue. Our system ensures you master each concept before moving forward.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Mandatory Pacing System
              </h3>
              <p className="text-gray-600">
                Stay on track with our intelligent pacing system that warns you if you fall behind schedule.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Free Remedial Coaching
              </h3>
              <p className="text-gray-600">
                Get free group coaching sessions if you need extra help. We're committed to your success.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Expert Tutors
              </h3>
              <p className="text-gray-600">
                Learn from admin-approved, experienced tutors who specialize in English test preparation.
              </p>
            </div>

            {/* Feature 5 */}
            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Comprehensive Content
              </h3>
              <p className="text-gray-600">
                Access high-quality video lessons, practice materials, and interactive assignments.
              </p>
            </div>

            {/* Feature 6 */}
            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Proven Results
              </h3>
              <p className="text-gray-600">
                Join thousands of successful students who achieved their target scores with our platform.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Test Categories Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Test Preparation Categories
            </h2>
            <p className="text-xl text-gray-600">
              Choose from our comprehensive range of English proficiency test preparations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* IELTS */}
            <Link href="/courses?category=IELTS" className="group">
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
                <div className="text-center">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-blue-600">IE</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">IELTS</h3>
                  <p className="text-gray-600 text-sm">
                    International English Language Testing System preparation
                  </p>
                </div>
              </div>
            </Link>

            {/* TOEFL */}
            <Link href="/courses?category=TOEFL" className="group">
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
                <div className="text-center">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-green-600">TO</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">TOEFL</h3>
                  <p className="text-gray-600 text-sm">
                    Test of English as a Foreign Language preparation
                  </p>
                </div>
              </div>
            </Link>

            {/* TOEIC */}
            <Link href="/courses?category=TOEIC" className="group">
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
                <div className="text-center">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-purple-600">TC</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">TOEIC</h3>
                  <p className="text-gray-600 text-sm">
                    Test of English for International Communication
                  </p>
                </div>
              </div>
            </Link>

            {/* General English */}
            <Link href="/courses?category=General" className="group">
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow group-hover:scale-105 transform transition-transform">
                <div className="text-center">
                  <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-orange-600">GE</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">General</h3>
                  <p className="text-gray-600 text-sm">
                    General English proficiency improvement courses
                  </p>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Start Your English Journey?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of successful students who achieved their target scores with our proven methodology.
          </p>
          <Link
            href="/courses"
            className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-block"
          >
            Browse All Courses
          </Link>
        </div>
      </section>
    </Layout>
  );
}